# WordPress BladeOne + <PERSON><PERSON><PERSON>CSS Boilerplate

A modern WordPress theme boilerplate featuring BladeOne templating engine, TailwindCSS utility framework, and a robust development workflow.

## 🚀 Features

- **BladeOne Templating** - Clean, expressive PHP templating with <PERSON><PERSON>'s Blade syntax
- **TailwindCSS v3.4+** - Utility-first CSS framework for rapid UI development
- **Modern Build System** - Webpack 5 with optimized development and production workflows
- **ACF Integration** - Custom ACF Framework for structured field management
- **Block Development** - Streamlined Gutenberg block creation with Blade views
- **WP-CLI Integration** - Custom commands for theme setup and block scaffolding
- **Code Quality Tools** - ESLint, Prettier, StyleLint, PHPCS, PHPStan
- **Performance Optimized** - Image optimization, asset minification, and caching
- **Developer Experience** - Hot reload, browser sync, and source maps

## 📋 Requirements

- **PHP** >= 8.0
- **WordPress** >= 6.0
- **Node.js** >= 20.0.0
- **npm** >= 10.0.0
- **Composer** >= 2.0

### Required WordPress Plugins
- Advanced Custom Fields PRO

## 🛠 Installation

### 1. Clone the Repository

```bash
# Navigate to your WordPress themes directory
cd wp-content/themes

# Clone the repository
git clone https://github.com/your-repo/wordpress-blade-theme.git your-theme-name

# Navigate to the theme directory
cd your-theme-name
```

### 2. Install Dependencies

```bash
# Install PHP dependencies
composer install

# Install Node dependencies
npm install
```

### 3. Theme Setup

Run the interactive theme setup to configure your theme:

```bash
npm run theme:setup
```

This will prompt you for:
- Theme Name
- Theme Description
- Author Name
- Author URI
- Theme Version
- Text Domain

### 4. Environment Configuration

Copy the example environment file:

```bash
cp .env.example .env
```

Edit `.env` to match your local development environment.

### 5. Build Assets

```bash
# Development build
npm run dev

# Production build
npm run prod

# Watch mode with hot reload
npm run watch
```

## 📁 Directory Structure

```
wordpress-blade-theme/
├── app/
│   ├── Core/                    # Core framework (protected)
│   │   ├── BladeOne/           # BladeOne templating engine
│   │   ├── AcfFramework/       # ACF field builder framework
│   │   └── Plugins/            # Core plugin integrations
│   ├── Boilerplate/            # Boilerplate functionality
│   │   ├── Blocks/             # Base block structures
│   │   ├── Helpers/            # Core helper classes
│   │   └── WpCli/              # WP-CLI commands
│   ├── Custom/                 # Project-specific code
│   │   ├── Blocks/             # Custom blocks
│   │   ├── CPT/                # Custom post types
│   │   ├── Components/         # Reusable components
│   │   └── Functions/          # Custom functions
│   ├── filters.php             # WordPress filters
│   ├── helpers.php             # Helper functions
│   └── setup.php               # Theme setup
├── resources/
│   ├── views/                  # Blade templates
│   │   ├── layouts/            # Page layouts
│   │   ├── partials/           # Reusable partials
│   │   ├── sections/           # Page sections
│   │   └── custom/             # Custom templates
│   ├── sass/                   # Styles
│   │   ├── core/               # Core styles
│   │   └── custom/             # Custom styles
│   └── js/                     # JavaScript
│       ├── core/               # Core scripts
│       └── custom/             # Custom scripts
├── build/                      # Compiled assets (gitignored)
├── node_modules/               # Node dependencies (gitignored)
├── vendor/                     # Composer dependencies (gitignored)
└── cache/                      # Blade cache (gitignored)
```

## 💻 Development Workflow

### Starting Development

```bash
# Start development server with hot reload
npm run watch

# Or run individual tasks
npm run dev          # Build development assets
npm run prod         # Build production assets
```

### Creating a New Block

Using WP-CLI:

```bash
wp create-block "Block Name"

# With template options
wp create-block --block-template="skeleton" --block-name="Hero Block"
```

Using npm:

```bash
npm run theme:create-block "Block Name"
```

### Code Quality

```bash
# Run all linters
npm run lint

# Fix linting issues
npm run lint-fix

# Run specific linters
npm run lint:eslint      # JavaScript linting
npm run lint:prettier    # Code formatting
npm run lint:php         # PHP linting

# PHP specific tools
composer run phpcs       # PHP CodeSniffer
composer run phpstan     # Static analysis
```

## 🎨 Customization Guidelines

### Adding Custom Styles

1. **Component Styles**: Create new SCSS files in `resources/sass/custom/`
2. **Import in app.scss**: `@import 'custom/your-component';`
3. **Use TailwindCSS**: Prefer utility classes over custom CSS

### Creating Blade Templates

1. **Page Templates**: Add to `resources/views/custom/`
2. **Reusable Components**: Add to `resources/views/components/`
3. **Use Blade Syntax**:

```blade
{{-- Extending a layout --}}
@extends('layouts.app')

@section('content')
    <div class="container mx-auto px-4">
        <h1 class="text-3xl font-bold">{{ $title }}</h1>
        
        @include('partials.content', ['data' => $data])
        
        @if($show_sidebar)
            @include('sections.sidebar')
        @endif
    </div>
@endsection
```

### Adding Custom Post Types

Create a new file in `app/Custom/CPT/`:

```php
<?php

namespace WordPressBladeTheme\Custom\CPT;

use WordPressBladeTheme\Helpers\RegisterCpt;

class Portfolio extends RegisterCpt
{
    protected string $postType = 'portfolio';
    protected string $singular = 'Portfolio Item';
    protected string $plural = 'Portfolio';
    protected string $icon = 'dashicons-portfolio';
    
    protected array $supports = [
        'title',
        'editor',
        'thumbnail',
        'excerpt',
        'custom-fields'
    ];
}
```

### Working with ACF Fields

Using the ACF Framework:

```php
use WordPressBladeTheme\Core\AcfFramework\FieldsBuilder;

$hero = new FieldsBuilder('hero_section');

$hero
    ->addText('title', [
        'label' => 'Hero Title',
        'required' => 1,
    ])
    ->addTextarea('description', [
        'label' => 'Hero Description',
        'rows' => 3,
    ])
    ->addImage('background_image', [
        'label' => 'Background Image',
        'return_format' => 'array',
    ])
    ->setLocation('page_template', '==', 'template-home.php');

add_action('acf/init', function() use ($hero) {
    acf_add_local_field_group($hero->build());
});
```

## 🔧 Configuration

### TailwindCSS Configuration

Edit `tailwind.config.js` to customize:

```javascript
module.exports = {
    content: [
        './app/**/*.php',
        './resources/**/*.{php,js}',
        './templates/**/*.php',
    ],
    theme: {
        extend: {
            colors: {
                primary: {
                    // Your custom color palette
                },
            },
            fontFamily: {
                // Your custom fonts
            },
        },
    },
    plugins: [
        require('@tailwindcss/forms'),
        require('@tailwindcss/typography'),
        require('@tailwindcss/container-queries'),
    ],
};
```

### Webpack Configuration

- `webpack.config.js` - Main assets build
- `webpack.admin.config.js` - Admin styles build
- `webpack.blocks.config.js` - Gutenberg blocks build

## 🚀 Deployment

### Production Build

```bash
# Build production assets
npm run prod

# Optional: analyze bundle size
npm run analyze
```

### Deployment Checklist

- [ ] Run production build
- [ ] Update version in `style.css` and `package.json`
- [ ] Clear Blade cache
- [ ] Test in staging environment
- [ ] Update `.env` for production
- [ ] Enable caching plugins
- [ ] Minify assets
- [ ] Optimize images

## 🐛 Troubleshooting

### Common Issues

**Blade Cache Issues**
```bash
# Clear Blade cache
rm -rf wp-content/cache/.wp-bladeone-cache/*
```

**Build Errors**
```bash
# Clean install
rm -rf node_modules package-lock.json
npm install
```

**PHP Autoload Issues**
```bash
composer dump-autoload
```

### Debug Mode

Enable WordPress debug mode in `wp-config.php`:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

## 📝 Best Practices

1. **Use Blade Components** for reusable UI elements
2. **Follow WordPress Coding Standards** enforced by PHPCS
3. **Utilize TailwindCSS utilities** instead of custom CSS
4. **Keep custom code in the `Custom` directory**
5. **Document complex functionality** with inline comments
6. **Test across browsers** and devices
7. **Optimize images** before committing
8. **Use semantic HTML** and ARIA labels
9. **Follow mobile-first** responsive design
10. **Cache expensive operations** appropriately

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Coding Standards

- JavaScript: ESLint + Prettier
- PHP: WordPress Coding Standards
- CSS/SCSS: StyleLint
- Commits: Conventional Commits

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [BladeOne](https://github.com/EFTEC/BladeOne) - PHP template engine
- [TailwindCSS](https://tailwindcss.com) - Utility-first CSS framework
- [WordPress](https://wordpress.org) - Content management system
- [Advanced Custom Fields](https://www.advancedcustomfields.com) - Custom fields framework

## 📞 Support

For issues and feature requests, please use the [GitHub issue tracker](https://github.com/your-repo/wordpress-blade-theme/issues).

---

Made with ❤️ by [Your Name](https://your-website.com)
