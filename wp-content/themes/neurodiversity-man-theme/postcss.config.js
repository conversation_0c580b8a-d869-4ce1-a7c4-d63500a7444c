module.exports = {
	plugins: {
		'tailwindcss/nesting': 'postcss-nesting',
		tailwindcss: {},
		'postcss-preset-env': {
			features: {
				'nesting-rules': false,
			},
			stage: 2,
			autoprefixer: {
				flexbox: 'no-2009',
				grid: 'autoplace',
			},
		},
		...(process.env.NODE_ENV === 'production'
			? {
					cssnano: {
						preset: [
							'default',
							{
								discardComments: {
									removeAll: true,
								},
								normalizeWhitespace: true,
								colormin: true,
								convertValues: true,
							},
						],
					},
				}
			: {}),
	},
};
