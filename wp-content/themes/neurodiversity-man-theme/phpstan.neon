includes:
	- vendor/szepeviktor/phpstan-wordpress/extension.neon

parameters:
	level: 6
	paths:
		- app
		- functions.php
		- index.php
	scanFiles:
		- %currentWorkingDirectory%/vendor/php-stubs/wordpress-stubs/wordpress-stubs.php
		- %currentWorkingDirectory%/vendor/php-stubs/acf-pro-stubs/acf-pro-stubs.php
	scanDirectories:
		- %currentWorkingDirectory%/vendor/illuminate
	excludePaths:
		- app/Core/BladeOne/bladeonecli
		- vendor
		- node_modules
		- build
		- cache
	ignoreErrors:
		# Ignore errors about undefined WordPress functions
		- '#Function (add_action|add_filter|wp_enqueue_script|wp_enqueue_style|get_template_directory_uri|get_stylesheet_directory) not found#'
		# Ignore errors about ACF functions
		- '#Function (get_field|the_field|have_rows|the_row|get_sub_field|the_sub_field) not found#'
		# Ignore errors about undefined constants that are defined in WordPress
		- '#Constant (ABSPATH|WP_DEBUG|WP_CONTENT_DIR) not found#'
		# Ignore errors about Blade directives
		- '#Call to an undefined method#'
	bootstrapFiles:
		- %currentWorkingDirectory%/vendor/autoload.php
	treatPhpDocTypesAsCertain: true
	checkMissingIterableValueType: false
	checkGenericClassInNonGenericObjectType: false
	reportUnmatchedIgnoredErrors: false 