<?php

/**
 * Theme setup.
 */

namespace NeurodiversityMan;

use NeurodiversityMan\Core\BladeOne\BladeOne;
use NeurodiversityMan\Helpers\BlockSetup;
use NeurodiversityMan\Helpers\OptionsPage;
use NeurodiversityMan\Helpers\RegisterCpt;
use NeurodiversityMan\WpCli\BlockCli;
use NeurodiversityMan\WpCli\ThemeCli;

// Register WP-CLI commands
if (defined('WP_CLI') && WP_CLI) {
	new BlockCli();
	new ThemeCli();
}

// Define theme constants
define('WPBB_JS_URI', trailingslashit(get_template_directory_uri()) . 'build/js/');
define('WPBB_JS_PATH', trailingslashit(get_template_directory()) . 'build/js/');
define('WPBB_CSS_URI', trailingslashit(get_template_directory_uri()) . 'build/css/');
define('WPBB_CSS_PATH', trailingslashit(get_template_directory()) . 'build/css/');
define('WPBB_IMG_URL', trailingslashit(get_template_directory_uri()) . 'build/images/');
define('WPBB_IMG_PATH', trailingslashit(get_template_directory()) . 'build/images/');

/**
 * Register the theme assets.
 *
 * @param array $css_dependencies
 * @param array $js_dependencies
 *
 * @return void
 */
function appAssets(array $css_dependencies = [], array $js_dependencies = []): void
{
	wp_enqueue_style(
		'wpbb-app',
		WPBB_CSS_URI . 'app.css',
		$css_dependencies,
		filemtime(WPBB_CSS_PATH . 'app.css'),
		false
	);

	wp_enqueue_script(
		'wpbb-app',
		WPBB_JS_URI . 'app.js',
		$js_dependencies,
		filemtime(WPBB_JS_PATH . 'app.js'),
		true
	);

	// Localize script for AJAX and other data
	wp_localize_script('wpbb-app', 'wpbbData', [
		'ajaxUrl' => admin_url('admin-ajax.php'),
		'nonce' => wp_create_nonce('wpbb-nonce'),
		'homeUrl' => home_url(),
		'themeUrl' => get_template_directory_uri(),
	]);
}

// Enqueue frontend assets
add_action('wp_enqueue_scripts', function () {
	appAssets();
}, 1);

// Enqueue admin/editor assets
add_action('admin_enqueue_scripts', function () {
	wp_enqueue_style(
		'wpbb-editor',
		WPBB_CSS_URI . 'editor.css',
		[],
		filemtime(WPBB_CSS_PATH . 'editor.css'),
		false
	);

	wp_enqueue_script(
		'wpbb-editor',
		WPBB_JS_URI . 'editor.js',
		[],
		filemtime(WPBB_JS_PATH . 'editor.js'),
		true
	);
});

// Enqueue block editor assets
add_action('enqueue_block_editor_assets', function () {
	wp_enqueue_style(
		'wpbb-block-editor',
		WPBB_CSS_URI . 'editor.css',
		[],
		filemtime(WPBB_CSS_PATH . 'editor.css'),
		false
	);
});

/**
 * Register the initial theme setup.
 *
 * @return void
 */
add_action('after_setup_theme', function () {

	/**
	 * Load theme text domain for translations
	 */
	load_theme_textdomain('neurodiversity-man', get_template_directory() . '/languages');

	/**
	 * Register the navigation menus.
	 *
	 * @link https://developer.wordpress.org/reference/functions/register_nav_menus/
	 */
	register_nav_menus([
		'primary_navigation' => __('Primary Navigation', 'neurodiversity-man'),
		'footer_navigation' => __('Footer Navigation', 'neurodiversity-man'),
		'mobile_navigation' => __('Mobile Navigation', 'neurodiversity-man'),
	]);

	/**
	 * Disable the default block patterns.
	 *
	 * @link https://developer.wordpress.org/block-editor/developers/themes/theme-support/#disabling-the-default-block-patterns
	 */
	remove_theme_support('core-block-patterns');

	/**
	 * Enable plugins to manage the document title.
	 *
	 * @link https://developer.wordpress.org/reference/functions/add_theme_support/#title-tag
	 */
	add_theme_support('title-tag');

	/**
	 * Enable post thumbnail support.
	 *
	 * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
	 */
	add_theme_support('post-thumbnails');

	/**
	 * Enable responsive embed support.
	 *
	 * @link https://wordpress.org/gutenberg/handbook/designers-developers/developers/themes/theme-support/#responsive-embedded-content
	 */
	add_theme_support('responsive-embeds');

	/**
	 * Enable HTML5 markup support.
	 *
	 * @link https://developer.wordpress.org/reference/functions/add_theme_support/#html5
	 */
	add_theme_support('html5', [
		'caption',
		'comment-form',
		'comment-list',
		'gallery',
		'search-form',
		'script',
		'style',
		'navigation-widgets',
	]);

	/**
	 * Enable selective refresh for widgets in customizer.
	 *
	 * @link https://developer.wordpress.org/themes/advanced-topics/customizer-api/#theme-support-in-sidebars
	 */
	add_theme_support('customize-selective-refresh-widgets');

	/**
	 * Enable Custom Logo Selection in customizer.
	 *
	 * @link https://developer.wordpress.org/themes/functionality/custom-logo/#adding-custom-logo-support-to-your-theme
	 */
	add_theme_support('custom-logo', [
		'height' => 100,
		'width' => 400,
		'flex-height' => true,
		'flex-width' => true,
		'header-text' => ['site-title', 'site-description'],
	]);

	/**
	 * Enable wide alignment support
	 */
	add_theme_support('align-wide');

	/**
	 * Enable editor styles
	 */
	add_theme_support('editor-styles');
	add_editor_style('build/css/editor.css');

	/**
	 * Add custom image sizes
	 */
	add_image_size('hero', 1920, 1080, true);
	add_image_size('card', 600, 400, true);
	add_image_size('square', 800, 800, true);

}, 20);

/**
 * Register the theme sidebars.
 *
 * @return void
 */
add_action('widgets_init', function () {
	$config = [
		'before_widget' => '<div class="widget %1$s %2$s">',
		'after_widget' => '</div>',
		'before_title' => '<h3 class="widget-title text-lg font-semibold mb-4">',
		'after_title' => '</h3>',
	];

	register_sidebar([
		'name' => __('Primary Sidebar', 'neurodiversity-man'),
		'id' => 'sidebar-primary',
		'description' => __('Main sidebar that appears on the right.', 'neurodiversity-man'),
	] + $config);

	register_sidebar([
		'name' => __('Footer Widget Area 1', 'neurodiversity-man'),
		'id' => 'footer-1',
		'description' => __('First footer widget area.', 'neurodiversity-man'),
		'before_title' => '<h4 class="widget-title text-base font-semibold mb-3">',
		'after_title' => '</h4>',
	] + $config);

	register_sidebar([
		'name' => __('Footer Widget Area 2', 'neurodiversity-man'),
		'id' => 'footer-2',
		'description' => __('Second footer widget area.', 'neurodiversity-man'),
		'before_title' => '<h4 class="widget-title text-base font-semibold mb-3">',
		'after_title' => '</h4>',
	] + $config);

	register_sidebar([
		'name' => __('Footer Widget Area 3', 'neurodiversity-man'),
		'id' => 'footer-3',
		'description' => __('Third footer widget area.', 'neurodiversity-man'),
		'before_title' => '<h4 class="widget-title text-base font-semibold mb-3">',
		'after_title' => '</h4>',
	] + $config);

	register_sidebar([
		'name' => __('Footer Widget Area 4', 'neurodiversity-man'),
		'id' => 'footer-4',
		'description' => __('Fourth footer widget area.', 'neurodiversity-man'),
		'before_title' => '<h4 class="widget-title text-base font-semibold mb-3">',
		'after_title' => '</h4>',
	] + $config);
});

// Initialize Blade instances
global $acfBlade;
$acfBlade = new BladeOne(__DIR__ . '/Blocks', WP_BLADEONE_CACHE, WP_BLADEONE_MODE);
wp_bladeone_blocks()->setCompiler($acfBlade);

global $bladeComponents;
$bladeComponents = new Core\BladeOne\BladeOne(WP_BLADEONE_COMPONENTS, WP_BLADEONE_CACHE, Core\BladeOne\BladeOne::MODE_SLOW);
wp_bladeone_components()->setCompiler($bladeComponents);

// ACF-specific initialization
if (class_exists('ACF')) {

	// Add ACF validation
	add_action('acf/validate_save_post', __NAMESPACE__ . '\\validate_save_post', 5);

	function validate_save_post(): void
	{
		// bail early if no $_POST
		if (empty($_POST)) {
			return;
		}
		foreach ($_POST as $key => $value) {
			if (str_starts_with($key, 'acf')) {
				if (!empty($value)) {
					acf_validate_values($value, $key);
				}
			}
		}
	}

	// Register CPTs and Blocks
	new RegisterCpt('init');
	new BlockSetup();

	// Initialize Options Page
	add_action('init', function () {
		new OptionsPage();
	}, 10);

	// Set Google Maps API key if configured
	add_action('init', function (): void {
		if (OptionsPage::get_option('api_keys')) {
			$api_keys = OptionsPage::get_option('api_keys');
			if (!empty($api_keys->google->backend_map)) {
				acf_update_setting('google_api_key', $api_keys->google->backend_map);
			}
		}
	});
}

// Load custom functions from the Custom directory
$custom_functions_dir = get_template_directory() . '/app/Custom/Functions';
if (is_dir($custom_functions_dir)) {
	foreach (glob($custom_functions_dir . '/*.php') as $file) {
		require_once $file;
	}
}


