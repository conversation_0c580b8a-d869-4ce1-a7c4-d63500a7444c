<?php

namespace NeurodiversityMan\Blocks\FooterBlock;

use NeurodiversityMan\Core\AcfFramework\FieldNameCollisionException;
use NeurodiversityMan\Core\AcfFramework\FieldsBuilder;
use NeurodiversityMan\Helpers\GlobalFunction;

/**
 * Example ACF Block
 */
class FooterBlock
{
	/**
	 * @var array
	 */
	protected array $config = [];

	/**
	 * @throws FieldNameCollisionException
	 */
	public function __construct()
	{
		self::setupFields();
		acf_add_local_field_group(self::registerFields());
	}

	protected function setupFields()
	{
		$this->setConfig([
			'name' => 'footer-block',
			'title' => 'Footer Block',
			'description' => 'This is an example Block',
			'keywords' => ['ACF', 'Footer', 'Block'],
			'mode' => 'preview'
		]);
	}

	/**
	 * @return array
	 * @throws FieldNameCollisionException
	 */
	protected function registerFields(): array
	{
		$block = new FieldsBuilder('footer-block');

		$block
			->setLocation('block', '==', 'acf/footer-block');

		$block->addGroup('Footer-block')
			->endGroup();

		return $block->build();
	}

	public function render($block)
	{
		$slug = str_replace('acf/', '', $block['name']);
		$classes = [$slug];
		$id = sprintf('%s-%s', $slug, $block['id']);
		if (!empty($block['anchor']))
			$id = $block['anchor'];

		if (!empty($block['className']))
			$classes[] = $block['className'];

		if (!empty($block['align']))
			$classes[] = 'align' . $block['align'];

		$block['className'] = implode(' ', $classes);

		$acfBlade = new \NeurodiversityMan\Core\BladeOne\BladeOne(__DIR__ . '/view/', WP_BLADEONE_CACHE, WP_BLADEONE_MODE);
		wp_bladeone()->setCompiler($acfBlade);

		global $bladeComponents;
		echo wp_bladeone_blocks()->run('FooterBlock.view.render', [
			'block' => $block,
			'data' => GlobalFunction::get_field('footer-block'),
			'components' => $bladeComponents
		]);
	}

	/**
	 * @return array
	 */
	public function getConfig(): array
	{
		return $this->config;
	}

	/**
	 * @param array $config
	 */
	public function setConfig(array $config): void
	{
		$this->config = $config;
	}

	/**
	 * @return string
	 */
	public function getPath(): string
	{
		return dirname(__FILE__);
	}

}
