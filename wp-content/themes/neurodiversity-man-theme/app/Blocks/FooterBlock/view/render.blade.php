@php
	/**
	* Footer Template.
	*
	* @param array $block The block settings and attributes.
	* @param string $data The block data.
	**/
@endphp


<footer id="{{ esc_attr($block['id']) }}" class="{{ esc_attr($block['className']) }} ">
	<div class="mx-auto w-full max-w-screen-xl p-4 py-6 lg:py-8 {{ GlobalFunction::get_theme_color_class(get_theme_mod('footer_background_color'), 'bg') }} {{ GlobalFunction::get_theme_color_class(get_theme_mod('footer_text_color'), 'text') }}">
		<div class="md:flex md:justify-between">
			<div class="mb-6 md:mb-0">
				<a href="{{ home_url('/') }}" class="flex items-center">
					@if(get_custom_logo())
						@php
							$custom_logo_id = get_theme_mod('custom_footer_logo');
							$logo_url = wp_get_attachment_image_url($custom_logo_id, 'full');
						@endphp
						<img src="{{ $logo_url }}" class="h-8 me-3" alt="{{ get_bloginfo('name', 'display') }}"/>
						@if(get_theme_mod('show_title_with_logo'))
							<span
									class="self-center text-2xl font-semibold whitespace-nowrap">{{ get_bloginfo('name', 'display') }}</span>
						@endif
					@else
						{!! get_bloginfo('name', 'display') !!}
					@endif
				</a>
			</div>
			<div class="widget-area grid grid-cols-2 gap-8 sm:gap-6 sm:grid-cols-3">
				@php(dynamic_sidebar('footer-1'))
				@php(dynamic_sidebar('footer-2'))
				@php(dynamic_sidebar('footer-3'))
			</div>
		</div>
		<hr class="my-6 border-gray-200 sm:mx-auto lg:my-8"/>
		<div class="sm:flex sm:items-center sm:justify-between">
          <span class="text-sm text-gray-500 sm:text-center">© {{ date('Y') }} <a
				  href="{{ home_url('/') }}" class="hover:underline">{!! get_bloginfo('name', 'display') !!}</a>. All Rights Reserved.
          </span>
			@php
				$social = \NeurodiversityMan\Helpers\OptionsPage::get_option('social')
			@endphp
			<div class="social-icons flex mt-4 sm:justify-center sm:mt-0">
				@unless(empty($social->facebook_page))
					<a href="#" class="social-icon text-gray-500 hover:text-gray-900">
						<svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
							 viewBox="0 0 8 19">
							<path fill-rule="evenodd"
								  d="M6.135 3H8V0H6.135a4.147 4.147 0 0 0-4.142 4.142V6H0v3h2v9.938h3V9h2.021l.592-3H5V3.591A.6.6 0 0 1 5.592 3h.543Z"
								  clip-rule="evenodd"/>
						</svg>
						<span class="sr-only">Facebook page</span>
					</a>
				@endunless
				@unless(empty($social->discord_community))
					<a href="#" class="social-icon text-gray-500 hover:text-gray-900">
						<svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
							 viewBox="0 0 21 16">
							<path
								d="M16.942 1.556a16.3 16.3 0 0 0-4.126-1.3 12.04 12.04 0 0 0-.529 1.1 15.175 15.175 0 0 0-4.573 0 11.585 11.585 0 0 0-.535-1.1 16.274 16.274 0 0 0-4.129 1.3A17.392 17.392 0 0 0 .182 13.218a15.785 15.785 0 0 0 4.963 2.521c.41-.564.773-1.16 1.084-1.785a10.63 10.63 0 0 1-1.706-.83c.143-.106.283-.217.418-.33a11.664 11.664 0 0 0 10.118 0c.137.113.277.224.418.33-.544.328-1.116.606-1.71.832a12.52 12.52 0 0 0 1.084 1.785 16.46 16.46 0 0 0 5.064-2.595 17.286 17.286 0 0 0-2.973-11.59ZM6.678 10.813a1.941 1.941 0 0 1-1.8-2.045 1.93 1.93 0 0 1 1.8-2.047 1.919 1.919 0 0 1 1.8 2.047 1.93 1.93 0 0 1-1.8 2.045Zm6.644 0a1.94 1.94 0 0 1-1.8-2.045 1.93 1.93 0 0 1 1.8-2.047 1.918 1.918 0 0 1 1.8 2.047 1.93 1.93 0 0 1-1.8 2.045Z"/>
						</svg>
						<span class="sr-only">Discord community</span>
					</a>
				@endunless
				@unless(empty($social->linkedin))
					<a href="#" class="social-icon text-gray-500 hover:text-gray-900">
						<svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
							 viewBox="0 0 24 24">
							<path fill-rule="evenodd"
								  d="M12.51 8.796v1.697a3.738 3.738 0 0 1 3.288-1.684c3.455 0 4.202 2.16 4.202 4.97V19.5h-3.2v-5.072c0-1.21-.244-2.766-2.128-2.766-1.827 0-2.139 1.317-2.139 2.676V19.5h-3.19V8.796h3.168ZM7.2 6.106a1.61 1.61 0 0 1-.988 1.483 1.595 1.595 0 0 1-1.743-.348A1.607 1.607 0 0 1 5.6 4.5a1.601 1.601 0 0 1 1.6 1.606Z"
								  clip-rule="evenodd"/>
							<path d="M7.2 8.809H4V19.5h3.2V8.809Z"/>
						</svg>
						<span class="sr-only">LinkedIN</span>
					</a>
				@endunless
				@unless(empty($social->twitter_page))
					<a href="#" class="social-icon text-gray-500 hover:text-gray-900">
						<svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
							 viewBox="0 0 20 17">
							<path fill-rule="evenodd"
								  d="M20 1.892a8.178 8.178 0 0 1-2.355.635 4.074 4.074 0 0 0 1.8-2.235 8.344 8.344 0 0 1-2.605.98A4.13 4.13 0 0 0 13.85 0a4.068 4.068 0 0 0-4.1 4.038 4 4 0 0 0 .105.919A11.705 11.705 0 0 1 1.4.734a4.006 4.006 0 0 0 1.268 5.392 4.165 4.165 0 0 1-1.859-.5v.05A4.057 4.057 0 0 0 4.1 9.635a4.19 4.19 0 0 1-1.856.07 4.108 4.108 0 0 0 3.831 2.807A8.36 8.36 0 0 1 0 14.184 11.732 11.732 0 0 0 6.291 16 11.502 11.502 0 0 0 17.964 4.5c0-.177 0-.35-.012-.523A8.143 8.143 0 0 0 20 1.892Z"
								  clip-rule="evenodd"/>
						</svg>
						<span class="sr-only">Twitter page</span>
					</a>
				@endunless
				@unless(empty($social->x_page))
					<a href="#" class="social-icon text-gray-500 hover:text-gray-900">
						<svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
							 viewBox="0 0 24 24">
							<path
								d="M13.795 10.533 20.68 2h-3.073l-5.255 6.517L7.69 2H1l7.806 10.91L1.47 22h3.074l5.705-7.07L15.31 22H22l-8.205-11.467Zm-2.38 2.95L9.97 11.464 4.36 3.627h2.31l4.528 6.317 1.443 2.02 6.018 8.409h-2.31l-4.934-6.89Z"/>
						</svg>
						<span class="sr-only">X page</span>
					</a>
				@endunless
				@unless(empty($social->github_account))
					<a href="#" class="social-icon text-gray-500 hover:text-gray-900">
						<svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
							 viewBox="0 0 20 20">
							<path fill-rule="evenodd"
								  d="M10 .333A9.911 9.911 0 0 0 6.866 19.65c.5.092.678-.215.678-.477 0-.237-.01-1.017-.014-1.845-2.757.6-3.338-1.169-3.338-1.169a2.627 2.627 0 0 0-1.1-1.451c-.9-.615.07-.6.07-.6a2.084 2.084 0 0 1 1.518 1.021 2.11 2.11 0 0 0 2.884.823c.044-.503.268-.973.63-1.325-2.2-.25-4.516-1.1-4.516-4.9A3.832 3.832 0 0 1 4.7 7.068a3.56 3.56 0 0 1 .095-2.623s.832-.266 2.726 1.016a9.409 9.409 0 0 1 4.962 0c1.89-1.282 2.717-1.016 2.717-1.016.366.83.402 1.768.1 2.623a3.827 3.827 0 0 1 1.02 2.659c0 3.807-2.319 4.644-4.525 4.889a2.366 2.366 0 0 1 .673 1.834c0 1.326-.012 2.394-.012 2.72 0 .263.18.572.681.475A9.911 9.911 0 0 0 10 .333Z"
								  clip-rule="evenodd"/>
						</svg>
						<span class="sr-only">GitHub account</span>
					</a>
				@endunless
				@unless(empty($social->youtube))
					<a href="#" class="social-icon text-gray-500 hover:text-gray-900">
						<svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
							 viewBox="0 0 24 24">
							<path fill-rule="evenodd"
								  d="M21.7 8.037a4.26 4.26 0 0 0-.789-1.964 2.84 2.84 0 0 0-1.984-.839c-2.767-.2-6.926-.2-6.926-.2s-4.157 0-6.928.2a2.836 2.836 0 0 0-1.983.839 4.225 4.225 0 0 0-.79 1.965 30.146 30.146 0 0 0-.2 3.206v1.5a30.12 30.12 0 0 0 .2 3.206c.094.712.364 1.39.784 1.972.604.536 1.38.837 2.187.848 1.583.151 6.731.2 6.731.2s4.161 0 6.928-.2a2.844 2.844 0 0 0 1.985-.84 4.27 4.27 0 0 0 .787-1.965 30.12 30.12 0 0 0 .2-3.206v-1.516a30.672 30.672 0 0 0-.202-3.206Zm-11.692 6.554v-5.62l5.4 2.819-5.4 2.801Z"
								  clip-rule="evenodd"/>
						</svg>
						<span class="sr-only">YouTube</span>
					</a>
				@endunless
				@unless(empty($social->instagram))
					<a href="#" class="social-icon text-gray-500 hover:text-gray-900">
						<svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
							 viewBox="0 0 24 24">
							<path fill="currentColor" fill-rule="evenodd"
								  d="M3 8a5 5 0 0 1 5-5h8a5 5 0 0 1 5 5v8a5 5 0 0 1-5 5H8a5 5 0 0 1-5-5V8Zm5-3a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h8a3 3 0 0 0 3-3V8a3 3 0 0 0-3-3H8Zm7.597 2.214a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2h-.01a1 1 0 0 1-1-1ZM12 9a3 3 0 1 0 0 6 3 3 0 0 0 0-6Zm-5 3a5 5 0 1 1 10 0 5 5 0 0 1-10 0Z"
								  clip-rule="evenodd"/>
						</svg>

						<span class="sr-only">Instagram</span>
					</a>
				@endunless
				@unless(empty($social->whatsapp))
					<a href="#" class="social-icon text-gray-500 hover:text-gray-900">
						<svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
							 viewBox="0 0 24 24">
							<path fill="currentColor" fill-rule="evenodd"
								  d="M12 4a8 8 0 0 0-6.895 12.06l.569.718-.697 2.359 2.32-.648.379.243A8 8 0 1 0 12 4ZM2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10a9.96 9.96 0 0 1-5.016-1.347l-4.948 1.382 1.426-4.829-.006-.007-.033-.055A9.958 9.958 0 0 1 2 12Z"
								  clip-rule="evenodd"/>
							<path fill="currentColor"
								  d="M16.735 13.492c-.038-.018-1.497-.736-1.756-.83a1.008 1.008 0 0 0-.34-.075c-.196 0-.362.098-.49.291-.146.217-.587.732-.723.886-.018.02-.042.045-.057.045-.013 0-.239-.093-.307-.123-1.564-.68-2.751-2.313-2.914-2.589-.023-.04-.024-.057-.024-.057.005-.021.058-.074.085-.101.08-.079.166-.182.249-.283l.117-.14c.121-.14.175-.25.237-.375l.033-.066a.68.68 0 0 0-.02-.64c-.034-.069-.65-1.555-.715-1.711-.158-.377-.366-.552-.655-.552-.027 0 0 0-.112.005-.137.005-.883.104-1.213.311-.35.22-.94.924-.94 2.16 0 1.112.705 2.162 1.008 2.561l.041.06c1.161 1.695 2.608 2.951 4.074 3.537 1.412.564 2.081.63 2.461.63.16 0 .288-.013.4-.024l.072-.007c.488-.043 1.56-.599 1.804-1.276.192-.534.243-1.117.115-1.329-.088-.144-.239-.216-.43-.308Z"/>
						</svg>
						<span class="sr-only">WhatsApp</span>
					</a>
				@endunless
			</div>
		</div>
	</div>
</footer>
