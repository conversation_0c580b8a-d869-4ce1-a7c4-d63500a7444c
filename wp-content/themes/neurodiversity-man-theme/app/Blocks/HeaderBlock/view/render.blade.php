@php
	/**
	 * Header Template.
	 *
	 * @param array 	$block The block settings and attributes.
	 * @param string 	$data The block data.
	 **/
@endphp

<header id="{{ esc_attr($block['id']) }}" class="{{ esc_attr($block['className']) }} ">
	<div class="py-2">
		<div class="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4">
			<a class="flex items-center space-x-3 rtl:space-x-reverse" href="{{ home_url('/') }}">
				@if(get_custom_logo())
					@php
						$custom_logo_id = get_theme_mod('custom_logo');
						$logo_url = wp_get_attachment_image_url($custom_logo_id, 'full');
					@endphp
					<img src="{{ $logo_url }}" class="h-8" alt="{{ get_bloginfo('name', 'display') }}"/>
					@if(get_theme_mod('show_title_with_logo'))
						<span
								class="self-center text-2xl font-semibold whitespace-nowrap">{{ get_bloginfo('name', 'display') }}</span>
					@endif
				@else
					<span
						class="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">{!! get_bloginfo('name', 'display') !!}</span>
				@endif
			</a>
			<button data-collapse-toggle="navbar-dropdown" type="button"
					class="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200"
					aria-controls="navbar-dropdown" aria-expanded="false">
				<span class="sr-only">Open main menu</span>
				<svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
					 viewBox="0 0 17 14">
					<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
						  d="M1 1h15M1 7h15M1 13h15"/>
				</svg>
			</button>
			{!! wp_nav_menu([
				'theme_location' => 'primary_navigation',
				'container' => 'div',
				'container_class' => 'hidden w-full md:block md:w-auto',
				'container_id' => 'navbar-dropdown',
				'menu_class' => 'flex flex-col font-medium p-4 md:p-0 mt-4 border border-gray-100 rounded-lg md:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0',
				'fallback_cb' => false,
//				'walker' => new \NeurodiversityMan\Helpers\NavWalker()
				]) !!}
		</div>
	</div>
</header>
