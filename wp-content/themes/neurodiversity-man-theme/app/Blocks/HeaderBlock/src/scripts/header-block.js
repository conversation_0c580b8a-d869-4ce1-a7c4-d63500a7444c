(function ($) {
	/**
	 * initializeBlock
	 *
	 * Adds custom JavaScript to the header-block HTML.
	 *
	 * @date    15/4/19
	 * @since   1.0.0
	 *
	 * @return  void
	 */

	const initializeBlock = function ($block) {};

	// Initialize each block on page load (front end).
	$(document).ready(function () {
		$('.header-block').each(function () {
			initializeBlock($(this));
		});
	});

	// Initialize dynamic block preview (editor).
	if (window.acf) {
		window.acf.addAction(
			'render_block_preview/type=header-block',
			function ($block) {
				initializeBlock($($block));
			}
		);
	}
})(jQuery);
