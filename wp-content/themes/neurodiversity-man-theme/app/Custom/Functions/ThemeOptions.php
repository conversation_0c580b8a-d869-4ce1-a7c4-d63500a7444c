<?php

namespace NeurodiversityMan\Custom\Functions;

use NeurodiversityMan\Core\AcfFramework\FieldsBuilder;

/**
 * Theme Options Page
 */
class ThemeOptions
{
    public function __construct()
    {
        add_action('acf/init', [$this, 'createOptionsPage']);
        add_action('acf/init', [$this, 'registerFields']);
    }

    public function createOptionsPage(): void
    {
        if (!function_exists('acf_add_options_page')) {
            return;
        }

        acf_add_options_page([
            'page_title' => __('Neurodiversity Man Settings', 'neurodiversity-man'),
            'menu_title' => __('Theme Settings', 'neurodiversity-man'),
            'menu_slug'  => 'neurodiversity-man-settings',
            'capability' => 'edit_theme_options',
            'icon_url'   => 'dashicons-admin-appearance',
            'position'   => 60,
        ]);
    }

    public function registerFields(): void
    {
        if (!function_exists('acf_add_local_field_group')) {
            return;
        }

        $settings = new FieldsBuilder('theme_settings');

        $settings
            ->addTab('general', ['label' => __('General', 'neurodiversity-man')])
                ->addImage('site_logo', [
                    'label' => __('Site Logo', 'neurodiversity-man'),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                ])
                ->addText('copyright_text', [
                    'label' => __('Copyright Text', 'neurodiversity-man'),
                    'default_value' => '© ' . date('Y') . ' Neurodiversity Man. All rights reserved.',
                ])
            ->addTab('social', ['label' => __('Social Media', 'neurodiversity-man')])
                ->addRepeater('social_links', [
                    'label' => __('Social Media Links', 'neurodiversity-man'),
                    'button_label' => __('Add Social Link', 'neurodiversity-man'),
                ])
                    ->addSelect('platform', [
                        'label' => __('Platform', 'neurodiversity-man'),
                        'choices' => [
                            'facebook' => 'Facebook',
                            'twitter' => 'Twitter',
                            'instagram' => 'Instagram',
                            'linkedin' => 'LinkedIn',
                            'youtube' => 'YouTube',
                            'github' => 'GitHub',
                        ],
                    ])
                    ->addUrl('url', [
                        'label' => __('URL', 'neurodiversity-man'),
                    ])
                ->endRepeater()
            ->setLocation('options_page', '==', 'neurodiversity-man-settings');

        acf_add_local_field_group($settings->build());
    }
}

// Initialize
new ThemeOptions();
