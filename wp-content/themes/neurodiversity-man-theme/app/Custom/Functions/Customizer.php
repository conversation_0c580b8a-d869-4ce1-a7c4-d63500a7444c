<?php

namespace NeurodiversityMan\Custom\Functions;

use WP_Customize_Color_Control;
use WP_Customize_Cropped_Image_Control;
use WP_Customize_Control;

/**
 * Custom Color Control with Theme Colors
 */
class Theme_Color_Control extends WP_Customize_Control
{
    public $type = 'theme-color';

    /**
     * Get theme colors from theme.json
     */
    private function get_theme_colors()
    {
        $theme_json_path = get_template_directory() . '/theme.json';

        if (!file_exists($theme_json_path)) {
            // Fallback colors if theme.json doesn't exist
            return [
                ['name' => 'Primary', 'color' => '#043873', 'slug' => 'primary'],
                ['name' => 'Secondary', 'color' => '#4F9CF9', 'slug' => 'secondary'],
                ['name' => 'Accent', 'color' => '#FFE492', 'slug' => 'accent'],
                ['name' => 'Body Text Dark', 'color' => '#212529', 'slug' => 'body-text-dark'],
                ['name' => 'Body Text Light', 'color' => '#ffffff', 'slug' => 'body-text-light'],
            ];
        }

        $theme_json = json_decode(file_get_contents($theme_json_path), true);

        if (!isset($theme_json['settings']['color']['palette'])) {
            return [];
        }

        $colors = [];
        foreach ($theme_json['settings']['color']['palette'] as $color) {
            $colors[] = [
                'name' => $color['name'],
                'color' => $color['color'],
                'slug' => $color['slug'] ?? sanitize_title($color['name'])
            ];
        }

        return $colors;
    }

    /**
     * Render the control content
     */
    public function render_content()
    {
        $theme_colors = $this->get_theme_colors();
        $current_value = $this->value();
        ?>
        <label>
            <span class="customize-control-title"><?php echo esc_html($this->label); ?></span>
            <?php if (!empty($this->description)) : ?>
                <span class="description customize-control-description"><?php echo $this->description; ?></span>
            <?php endif; ?>
        </label>

        <div class="theme-color-control-wrapper">
            <!-- Theme Color Palette -->
            <div class="theme-color-palette">
                <h4 style="margin: 10px 0 5px 0; font-size: 12px; font-weight: 600; text-transform: uppercase;">Theme Colors</h4>
                <div class="theme-color-swatches" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(40px, 1fr)); gap: 8px; margin-bottom: 15px;">
                    <?php foreach ($theme_colors as $color) : ?>
                        <div class="theme-color-swatch"
                             data-color="<?php echo esc_attr($color['color']); ?>"
                             title="<?php echo esc_attr($color['name']); ?>"
                             style="width: 40px; height: 40px; border-radius: 4px; border: 2px solid #ddd; cursor: pointer; background-color: <?php echo esc_attr($color['color']); ?>; transition: all 0.2s ease; position: relative;">
                        </div>
                    <?php endforeach; ?>

                    <!-- Clear/Custom Color Button -->
                    <div class="theme-color-swatch custom-color-trigger"
                         title="Custom Color"
                         style="width: 40px; height: 40px; border-radius: 4px; border: 2px solid #ddd; cursor: pointer; background: linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%); background-size: 8px 8px; background-position: 0 0, 0 4px, 4px -4px, -4px 0px; transition: all 0.2s ease; position: relative; display: flex; align-items: center; justify-content: center; font-size: 18px; color: #666;">
                        +
                    </div>
                </div>
            </div>

            <!-- Hidden color input for custom colors -->
            <input type="color"
                   class="theme-color-input"
                   value="<?php echo esc_attr($current_value); ?>"
                   style="width: 100%; height: 40px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 10px; display: none;" />

            <!-- Text input for hex values -->
            <input type="text"
                   class="theme-color-hex"
                   value="<?php echo esc_attr($current_value); ?>"
                   placeholder="#000000"
                   style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace;" />
        </div>

        <style>
            .theme-color-swatch:hover {
                transform: scale(1.1);
                border-color: #0073aa !important;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            }

            .theme-color-swatch.selected {
                border-color: #0073aa !important;
                box-shadow: 0 0 0 2px #0073aa;
                transform: scale(1.05);
            }

            .custom-color-trigger:hover {
                background-color: #f0f0f0;
                color: #0073aa;
            }
        </style>

        <script>
        (function($) {
            $(document).ready(function() {
                var control = $('#customize-control-<?php echo $this->id; ?>');
                var input = control.find('.theme-color-hex');
                var colorInput = control.find('.theme-color-input');
                var swatches = control.find('.theme-color-swatch');
                var customTrigger = control.find('.custom-color-trigger');

                // Update selected state
                function updateSelected(color) {
                    swatches.removeClass('selected');
                    swatches.each(function() {
                        if ($(this).data('color') && $(this).data('color').toLowerCase() === color.toLowerCase()) {
                            $(this).addClass('selected');
                        }
                    });
                }

                // Initialize selected state
                updateSelected(input.val());

                // Handle swatch clicks
                swatches.not('.custom-color-trigger').on('click', function() {
                    var color = $(this).data('color');
                    input.val(color).trigger('change');
                    colorInput.val(color);
                    updateSelected(color);
                });

                // Handle custom color trigger
                customTrigger.on('click', function() {
                    colorInput.show().trigger('click');
                });

                // Handle color input change
                colorInput.on('change', function() {
                    var color = $(this).val();
                    input.val(color).trigger('change');
                    updateSelected(color);
                    $(this).hide();
                });

                // Handle text input change
                input.on('change keyup', function() {
                    var color = $(this).val();
                    colorInput.val(color);
                    updateSelected(color);
                });
            });
        })(jQuery);
        </script>
        <?php
    }
}

/**
 * WordPress Customizer functionality
 */
class Customizer
{
    public function __construct()
    {
        add_action('customize_register', [$this, 'customize_register']);
        add_action('customize_controls_enqueue_scripts', [$this, 'enqueue_customizer_assets']);
    }

    /**
     * Customize register callback
     */
    public function customize_register($wp_customize): void
    {
        // Test with a completely basic setting first
        $wp_customize->add_setting(
            'custom_footer_logo',
            array(
                'theme_supports' => array('custom-logo'),
                'transport' => 'postMessage',
            )
        );

        $custom_logo_args = get_theme_support('custom-logo');
        $wp_customize->add_control(
            new WP_Customize_Cropped_Image_Control(
                $wp_customize,
                'custom_footer_logo',
                array(
                    'label' => __('Logo'),
                    'section' => 'title_tagline',
                    'priority' => 8,
                    'height' => isset($custom_logo_args[0]['height']) ? $custom_logo_args[0]['height'] : null,
                    'width' => isset($custom_logo_args[0]['width']) ? $custom_logo_args[0]['width'] : null,
                    'flex_height' => isset($custom_logo_args[0]['flex-height']) ? $custom_logo_args[0]['flex-height'] : null,
                    'flex_width' => isset($custom_logo_args[0]['flex-width']) ? $custom_logo_args[0]['flex-width'] : null,
                    'button_labels' => array(
                        'select' => __('Select logo'),
                        'change' => __('Change logo'),
                        'remove' => __('Remove'),
                        'default' => __('Default'),
                        'placeholder' => __('No logo selected'),
                        'frame_title' => __('Select logo'),
                        'frame_button' => __('Choose logo'),
                    ),
                )
            )
        );

        $wp_customize->selective_refresh->add_partial(
            'custom_footer_logo',
            array(
                'settings' => array('custom_footer_logo'),
                'selector' => '.custom-logo-link',
                'render_callback' => array($wp_customize, '_render_custom_logo_partial'),
                'container_inclusive' => true,
            )
        );
        // Add setting to show/hide title with logo
        $wp_customize->add_setting('show_title_with_logo', [
            'default' => true,
            'transport' => 'postMessage',
            'sanitize_callback' => 'wp_validate_boolean',
        ]);

        $wp_customize->add_control('show_title_with_logo', [
            'label' => __('Show Title with Logo', 'neurodiversity-man'),
            'section' => 'title_tagline',
            'type' => 'checkbox',
            'priority' => 10,
        ]);

        // Create custom section for header/footer colors
        $wp_customize->add_section('header_footer_colors', [
            'title' => __('Header & Footer Colors', 'neurodiversity-man'),
            'priority' => 30,
        ]);
        $wp_customize->add_section('header_colors', [
            'title' => __('Header', 'neurodiversity-man'),
            'priority' => 30,
        ]);

        // Header background color
        $wp_customize->add_setting('header_background_color', [
            'default' => '#ffffff',
            'transport' => 'postMessage',
            'sanitize_callback' => 'sanitize_hex_color',
        ]);

        $wp_customize->add_control(
            new Theme_Color_Control(
                $wp_customize,
                'header_background_color',
                [
                    'label' => __('Header Background Color', 'neurodiversity-man'),
                    'section' => 'header_footer_colors',
                    'priority' => 1,
                    'description' => __('Choose from theme colors or select a custom color.', 'neurodiversity-man'),
                ]
            )
        );

        // Header text color
        $wp_customize->add_setting('header_text_color', [
            'default' => '#333333',
            'transport' => 'postMessage',
            'sanitize_callback' => 'sanitize_hex_color',
        ]);

        $wp_customize->add_control(
            new Theme_Color_Control(
                $wp_customize,
                'header_text_color',
                [
                    'label' => __('Header Text Color', 'neurodiversity-man'),
                    'section' => 'header_footer_colors',
                    'priority' => 2,
                    'description' => __('Choose from theme colors or select a custom color.', 'neurodiversity-man'),
                ]
            )
        );

        // Footer background color
        $wp_customize->add_setting('footer_background_color', [
            'default' => '#333333',
            'transport' => 'postMessage',
            'sanitize_callback' => 'sanitize_hex_color',
        ]);

        $wp_customize->add_control(
            new Theme_Color_Control(
                $wp_customize,
                'footer_background_color',
                [
                    'label' => __('Footer Background Color', 'neurodiversity-man'),
                    'section' => 'header_footer_colors',
                    'priority' => 3,
                    'description' => __('Choose from theme colors or select a custom color.', 'neurodiversity-man'),
                ]
            )
        );

        // Footer text color
        $wp_customize->add_setting('footer_text_color', [
            'default' => '#ffffff',
            'transport' => 'postMessage',
            'sanitize_callback' => 'sanitize_hex_color',
        ]);

        $wp_customize->add_control(
            new Theme_Color_Control(
                $wp_customize,
                'footer_text_color',
                [
                    'label' => __('Footer Text Color', 'neurodiversity-man'),
                    'section' => 'header_footer_colors',
                    'priority' => 4,
                    'description' => __('Choose from theme colors or select a custom color.', 'neurodiversity-man'),
                ]
            )
        );
    }

    /**
     * Enqueue customizer assets
     */
    public function enqueue_customizer_assets()
    {
        wp_enqueue_style(
            'theme-customizer-controls',
            get_template_directory_uri() . '/assets/css/customizer-controls.css',
            [],
            wp_get_theme()->get('Version')
        );
    }
}

// Initialize the Customizer class
new Customizer();
