<?php

/**
 * Theme filters.
 */

namespace JBC;

/**
 * Add "… Continued" to the excerpt.
 *
 * @return string
 */
add_filter('excerpt_more', function () {
	if(is_search()) {return '';}
	return sprintf(' &hellip; <a class="text-accent mt-2" href="%s">%s</a>', get_permalink(), __('Read More', 'neurodiversity-man'));
});

/**
 * Add Custom Variables
 */

add_filter('query_vars', function ($vars) {
	$vars[] = '';
	return $vars;
});

/**
 * Template Hierarchy should search for .blade.php files
 */
collect([
	'index', '404', 'archive', 'author', 'category', 'tag', 'taxonomy', 'date', 'home',
	'frontpage', 'page', 'paged', 'search', 'single', 'singular', 'attachment', 'embed'
])->map(function ($type) {
	add_filter("{$type}_template_hierarchy", __NAMESPACE__ . '\\filter_templates');
});

/**
 * Render page using Blade
 */
add_filter('template_include', function ($template) {

	$data = collect(get_body_class())->reduce(function ($data, $class) use ($template) {
		return apply_filters("JBC/template/{$class}/data", $data, $template);
	}, []);
	if ($template) {
		if (preg_match("/\/resources\/views.*\.blade\.php/", $template)) {
			echo template($template, $data);
			return get_stylesheet_directory() . '/index.php';
		}
	}
	return $template;
}, PHP_INT_MAX);

/**
 * Render comments.blade.php
 */
add_filter('comments_template', function ($comments_template) {

	$data = collect(get_body_class())->reduce(function ($data, $class) use ($comments_template) {
		return apply_filters("jbc/template/{$class}/data", $data, $comments_template);
	}, []);

	$theme_template = locate_template(["views/partials/{$comments_template}", $comments_template]);

	if ($theme_template) {
		echo template($theme_template, $data);
		return get_stylesheet_directory() . '/index.php';
	}

	return $comments_template;
}, 100);

add_filter('body_class', function (array $classes) {
	/** Add page slug if it doesn't exist */
	if (is_single() || is_page() && !is_front_page()) {
		if (!in_array(basename(get_permalink()), $classes)) {
			$classes[] = basename(get_permalink());
		}
	}

	/** Add class if sidebar is active */
	if (display_sidebar()) {
		$classes[] = 'sidebar-primary';
	}

	/** Clean up class names for custom templates */
	$classes = array_map(function ($class) {
		return preg_replace(['/-blade(-php)?$/', '/^page-template-views/'], '', $class);
	}, $classes);

	return array_filter($classes);
});


add_filter('get_custom_logo', function ($html) {
	$html = str_replace('<a', '<a class="toggleColour text-black no-underline hover:no-underline font-bold text-2xl lg:text-4xl"', $html);
	return $html;
});


/**
 * Enable SVG Support
 */
add_filter('upload_mimes', function ($mimes) {
	$mimes['svg'] = 'image/svg+xml';

	return $mimes;
});

add_filter('wp_check_filetype_and_ext', function ($data, $file, $filename, $mimes) {
	if (isset($file['type']) && $file['type'] == 'image/svg+xml') {
		$data['type'] = 'image/svg+xml';
	}

	return $data;
}, 10, 4);

/**
 * Update Search From HTML
 */

add_filter('get_search_form', function ($form) {
	return '<form class="mt-4 w-full gap-4 sm:flex lg:mt-0"
					  action="'. home_url( '/' ) .'" method="get">
					<label for="default-search"
						   class="mb-2 text-sm font-medium text-gray-900 sr-only">'. __( 'Search for:' ) .'</label>
					<div class="relative w-full flex-1">
						<div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
							<svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
								 fill="none" viewBox="0 0 20 20">
								<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
									  stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
							</svg>
						</div>
						<input name="s" type="search" id="default-search"
							   class="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500"
							   placeholder="Search..." value="'. get_search_query() .'" required/>
						<button type="submit"
								class="text-white absolute end-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2">'. esc_attr__( 'Search' ) .'</button>
					</div>
				</form>';
});
/**
 * Nav Menu Add Custom Class Arguments.
 */
add_filter('nav_menu_css_class', function ($classes, $item, $args) {
	if (isset($args->items_class)) {
		$classes[] = $args->items_class;
	}
	return $classes;
}, 1, 3);


add_filter('nav_menu_link_attributes', function ($atts, $item, $args) {
	if (property_exists($args, 'link_class')) {
		$atts['class'] = $args->link_class;
	}
	return $atts;
}, 1, 3);
