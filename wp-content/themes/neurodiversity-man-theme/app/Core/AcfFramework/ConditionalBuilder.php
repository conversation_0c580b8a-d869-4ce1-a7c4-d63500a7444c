<?php

namespace NeurodiversityMan\Core\AcfFramework;

/**
 * @method ConditionalBuilder and ( string $name, string $operator, string $value )
 * @method ConditionalBuilder or ( string $name, string $operator, string $value )
 * @method FieldBuilder addField( string $name, string $type, array $args = [] )
 * @method FieldBuilder addFields( FieldsBuilder|array $fields )
 * @method FieldBuilder addChoiceField( string $name, string $type, array $args = [] )
 * @method FieldBuilder addText( string $name, array $args = [] )
 * @method FieldBuilder addTextarea( string $name, array $args = [] )
 * @method FieldBuilder addNumber( string $name, array $args = [] )
 * @method FieldBuilder addEmail( string $name, array $args = [] )
 * @method FieldBuilder addUrl( string $name, array $args = [] )
 * @method FieldBuilder addPassword( string $name, array $args = [] )
 * @method FieldBuilder addWysiwyg( string $name, array $args = [] )
 * @method FieldBuilder addOembed( string $name, array $args = [] )
 * @method FieldBuilder addImage( string $name, array $args = [] )
 * @method FieldBuilder addFile( string $name, array $args = [] )
 * @method FieldBuilder addGallery( string $name, array $args = [] )
 * @method FieldBuilder addTrueFalse( string $name, array $args = [] )
 * @method FieldBuilder addSelect( string $name, array $args = [] )
 * @method FieldBuilder addRadio( string $name, array $args = [] )
 * @method FieldBuilder addCheckbox( string $name, array $args = [] )
 * @method FieldBuilder addPostObject( string $name, array $args = [] )
 * @method FieldBuilder addPageLink( string $name, array $args = [] )
 * @method FieldBuilder addTaxonomy( string $name, array $args = [] )
 * @method FieldBuilder addUser( string $name, array $args = [] )
 * @method FieldBuilder addDatePicker( string $name, array $args = [] )
 * @method FieldBuilder addTimePicker( string $name, array $args = [] )
 * @method FieldBuilder addDateTimePicker( string $name, array $args = [] )
 * @method FieldBuilder addColorPicker( string $name, array $args = [] )
 * @method FieldBuilder addGoogleMap( string $name, array $args = [] )
 * @method FieldBuilder addLink( string $name, array $args = [] )
 * @method FieldBuilder addTab( string $label, array $args = [] )
 * @method FieldBuilder addRange( string $name, array $args = [] )
 * @method FieldBuilder addMessage( string $label, string $message, array $args = [] )
 * @method FieldBuilder addRelationship( string $name, array $args = [] )
 * @method GroupBuilder addGroup( string $name, array $args = [] )
 * @method GroupBuilder endGroup()
 * @method RepeaterBuilder addRepeater( string $name, array $args = [] )
 * @method RepeaterBuilder endRepeater()
 * @method FlexibleContentBuilder addFlexibleContent( string $name, array $args = [] )
 * @method FieldsBuilder addLayout( string|FieldsBuilder $layout, array $args = [] )
 * @method LocationBuilder setLocation( string $param, string $operator, string $value )
 */
class ConditionalBuilder extends ParentDelegationBuilder
{
    /**
     * Conditional Rules
     * @var array[array]
     */
    private $config = [[]];

    /**
     * Creates the first rule. Additional rules can be chained use `or` and `and`
     * @param string $name
     * @param string $operator
     * @param string $value
     */
    public function __construct($name, $operator, $value)
    {
        $this->and($name, $operator, $value);
    }

    /**
     * Build the config
     * @return array
     */
    public function build()
    {
        return $this->config;
    }

    /**
     * Allow the use of reserved words and / or for methods. If `and` or `or`
     * are not matched, call the method on the parentContext
     * @param string $methodName
     * @param array $arguments
     * @return mixed
     */
    public function __call($methodName, $arguments)
    {
        if ($methodName === 'and') {
            [$name, $operator, $value] = $arguments;
            return $this->andCondition($name, $operator, $value);
        } elseif ($methodName === 'or') {
            [$name, $operator, $value] = $arguments;
            return $this->orCondition($name, $operator, $value);
        } else {
            return parent::__call($methodName, $arguments);
        }
    }

    /**
     * Creates an AND condition
     * @param string $name
     * @param string $operator
     * @param string $value
     * @return $this
     */
    public function andCondition($name, $operator, $value)
    {
        $orCondition = $this->popOrCondition();
        $orCondition[] = $this->createCondition($name, $operator, $value);
        $this->pushOrCondition($orCondition);

        return $this;
    }

    /**
     * Removes and returns the last top level OR condition
     * @return array
     */
    protected function popOrCondition()
    {
        return array_pop($this->config);
    }

    /**
     * Creates a condition
     * @param string $name
     * @param string $operator
     * @param string $value
     * @return array
     */
    protected function createCondition($name, $operator, $value)
    {
        return [
            'field' => $name,
            'operator' => $operator,
            'value' => $value,
        ];
    }

    /**
     * Adds a top level OR condition
     * @param array $condition
     * @return void
     */
    protected function pushOrCondition($condition)
    {
        $this->config[] = $condition;
    }

    /**
     * Creates an OR condition
     * @param string $name
     * @param string $operator
     * @param string $value
     * @return $this
     */
    public function orCondition($name, $operator, $value)
    {
        $condition = $this->createCondition($name, $operator, $value);
        $this->pushOrCondition([$condition]);

        return $this;
    }
}
