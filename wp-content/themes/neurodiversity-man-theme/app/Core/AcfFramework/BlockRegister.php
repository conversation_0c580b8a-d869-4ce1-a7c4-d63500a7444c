<?php

namespace NeurodiversityMan\Core\AcfFramework;

use NeurodiversityMan\Helpers\GlobalFunction;

class BlockRegister {
	private \stdClass|array|bool $config;
	private mixed $className;

	public function __construct( $className ) {
		$this->className = new $className;
		$this->config    = GlobalFunction::array_to_object( $this->className->getConfig() );

	}

	public function registerBlock(): void
	{

		if ( ! function_exists( 'acf_register_block_type' ) ) {
			return;
		}

		$styles = [];

		if ( isset( $this->config->styles ) ) {
			$styles = GlobalFunction::object_to_array( $this->config->styles );
		}

		$supports = [
			'align'         => true,
			'mode'          => true,
			'multiple'      => true,
			'jsx'           => true,
			'align_content' => false,
			'anchor'        => true,
			'color'         => [
				'text'       => true,
				'background' => true,
				'link'       => true
			],
			'spacing'       => [
				'margin'   => [
					'bottom',
					'top'
				], // Enable margin for arbitrary sides.
				'padding'  => true, // Enable padding for all sides.
				'blockGap' => [
					'horizontal',
					'vertical'
				] // Enables axial (column/row) block spacing controls.
			],
			'background'   => [
				'backgroundImage' => true, // Enable background image control.
				'backgroundSize'  => true // Enable background image + size control.
			],
			'align_text' 	=>	true,
			'typography'	=>	['lineHeight'=>true, 'fontSize'=>true,'fontWeight' => true,'fontStyle' => true,'textTransform' => true],
			'full_height'	=>	true,
		];
		if ( isset( $this->config->supports ) ) {
			$this->config->supports = GlobalFunction::object_to_array( $this->config->supports );
			$this->config->supports = array_replace_recursive( $supports, $this->config->supports );
			$this->config->supports = GlobalFunction::array_to_object( $this->config->supports );
		}else{
			$this->config->supports = GlobalFunction::array_to_object($supports);
		}
		$default = [
			'name'            => 'new-block',
			'title'           => 'new-block',
			'description'     => '',
			'category'        => 'jbc-theme-blocks',
			'keywords'        => [ 'New Block' ],
			'mode'            => 'preview',
			'align'           => 'full',
			'align_content'   => null,
			'render_template' => $this->className->getPath() . '/view/render.php',
			'render_callback' => '',
			'enqueue_style'   => '',
			'enqueue_script'  => '',
			'enqueue_assets'  => '',
			'icon'            => '',
			'styles'          => $styles,
			'supports' => $this->config->supports,
			'example'         => [
				'attributes' => [
					'mode' => 'preview',
					'data' => [
						'_is_preview' => 'true'
					]
				]
			],
			'active'          => true,
		];

		if ( isset( $this->config ) ) {
			$this->config = GlobalFunction::object_to_array( $this->config );
			$this->config = array_replace_recursive( $default, $this->config );
			$this->config = GlobalFunction::array_to_object( $this->config );
		}

		acf_register_block_type( [
			'name'            => $this->config->name,
			'title'           => $this->config->title,
			'description'     => $this->config->description,
			'category'        => $this->config->category,
			'keywords'        => GlobalFunction::object_to_array( $this->config->keywords ),
			'mode'            => $this->config->mode,
			'align'           => $this->config->align,
			'align_content'   => $this->config->align_content,
			'render_template' => $this->config->render_template,
			'render_callback' => $this->config->render_callback = isset( $this->config->render_callback ) ? [
				$this,
				'render'
			] : '',
			'enqueue_style'   => $this->config->enqueue_style,
			'enqueue_script'  => $this->config->enqueue_script,
			'enqueue_assets'  => $this->config->enqueue_assets,
			'icon'            => $this->config->icon,
			'styles'          => $styles,
			'supports'        => GlobalFunction::object_to_array( $this->config->supports ),
			'example'         => $this->config->example,
			'active'          => true,
		] );
	}

	public function render( $block ): void
	{
		if ( ! empty( $block['data']['_is_preview'] ) && file_exists( WPBB_IMG_PATH . str_replace( 'acf/', '', $block['name'] ) . ".png" ) ) {
			echo '<img src="' . WPBB_IMG_URL . str_replace( 'acf/', '', $block['name'] ) . ".png" . '" style="max-width: 450px;height: auto;">';
		} else {
			$this->className->render( $block );
		}
	}

}
