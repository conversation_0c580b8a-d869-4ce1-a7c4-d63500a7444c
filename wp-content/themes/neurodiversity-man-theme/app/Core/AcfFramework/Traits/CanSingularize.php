<?php

namespace NeurodiversityMan\Core\AcfFramework\Traits;

use Doctrine\Inflector\InflectorFactory;

trait CanSingularize
{
    /**
     * Return a singularized string.
     * @param string $value
     * @return string
     */
    protected function singularize($value)
    {
        if (class_exists('\Doctrine\Inflector\InflectorFactory')) {
            return InflectorFactory::create()->build()->singularize($value);
        }

//        return \Doctrine\Common\Inflector\Inflector::singularize($value);
    }
}
