<?php

namespace NeurodiversityMan\Core\AcfFramework\Transform;

use NeurodiversityMan\Core\AcfFramework\Builder;
use NeurodiversityMan\Core\AcfFramework\FieldsBuilder;

/**
 * Replace the field name in the 'field' key, with the key of the
 * actual field as defined by the Builder.
 */
class ConditionalField extends RecursiveTransform
{
    protected $keys = ['field'];

    /**
     * @param FieldsBuilder $builder
     */
    public function __construct(FieldsBuilder $builder)
    {
        parent::__construct($builder);
    }

    public function transformValue($value)
    {
        if ($this->getBuilder()->fieldExists($value)) {
            return $this->getBuilder()->getField($value)->getKey();
        }

        return $value;
    }

    /**
     * @return FieldsBuilder
     */
    public function getBuilder(): Builder
    {
        return parent::getBuilder();
    }

    public function transformConfig($config)
    {
        if ($this->getBuilder()->fieldExists($config['field'])
            && $this->getBuilder()->getField($config['field'])->hasCustomKey()) {
            $config['_has_custom_key'] = TRUE;
        } elseif (!$this->getBuilder()->fieldExists($config['field'])) {
            $config['_field_does_not_exist'] = $config['field'];
        }

        return $config;
    }

    public function shouldTransformValue($key, $config)
    {
        return parent::shouldTransformValue($key, $config)
            && !(array_key_exists('_has_custom_key', $config)
                && $config['_has_custom_key'] === TRUE);
    }
}
