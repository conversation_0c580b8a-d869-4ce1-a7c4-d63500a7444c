<?php

namespace NeurodiversityMan\Core\AcfFramework\Transform;

use NeurodiversityMan\Core\AcfFramework\Builder;
use NeurodiversityMan\Core\AcfFramework\FieldsBuilder;

/**
 * Applies the ConditionalField Transform to the conditional_logic value
 * of each field, in the field group config.
 */
class ConditionalLogic extends IterativeTransform
{
    protected $keys = ['conditional_logic'];

    /**
     * @param FieldsBuilder $builder
     */
    public function __construct(FieldsBuilder $builder)
    {
        parent::__construct($builder);
    }

    /**
     * Replace field values of a ConditionalBuilder with the proper keys using
     * the ConditionalField Transform.
     *
     * @param array $value
     * @return array Transformed config array
     */
    public function transformValue($value)
    {
        $conditionalFieldTransform = new ConditionalField($this->getBuilder());
        return $conditionalFieldTransform->transform($value);
    }

    /**
     * @return FieldsBuilder
     */
    public function getBuilder(): Builder
    {
        return parent::getBuilder();
    }

    public function transform($config)
    {
        return parent::transform($config);
    }
}
