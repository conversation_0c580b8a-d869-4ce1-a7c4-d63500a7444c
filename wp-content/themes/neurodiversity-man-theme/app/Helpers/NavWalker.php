<?php

namespace NeurodiversityMan\Helpers;

use Walker_Nav_Menu;

class NavWalker extends Walker_Nav_Menu
{
	// Start Level
	function start_lvl(&$output, $depth = 0, $args = null)
	{
		if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
			$t = '';
			$n = '';
		} else {
			$t = "\t";
			$n = "\n";
		}
		$indent = str_repeat($t, $depth);
		$classes = array('sub-menu');
		$class_names = join(' ', apply_filters('nav_menu_submenu_css_class', $classes, $args, $depth));
		$class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

		$output .= "{$n}{$indent}<div class=\"dropdownNavbar absolute bg-white z-10 hidden font-normal divide-y divide-gray-100 rounded-lg shadow w-44\"><ul{$class_names}>{$n}";
	}

	// End Level
	function end_lvl(&$output, $depth = 0, $args = null)
	{
		if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
			$t = '';
			$n = '';
		} else {
			$t = "\t";
			$n = "\n";
		}
		$indent = str_repeat($t, $depth);
		$output .= "$indent</ul></div>{$n}";
	}

	// Start Element
	function start_el(&$output, $item, $depth = 0, $args = null, $id = 0)
	{
		if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
			$t = '';
			$n = '';
		} else {
			$t = "\t";
			$n = "\n";
		}
		$indent = ($depth) ? str_repeat($t, $depth) : '';

		$classes = empty($item->classes) ? array() : (array)$item->classes;
		$classes[] = 'menu-item-' . $item->ID;

		$class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args, $depth));
		$class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

		$id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args, $depth);
		$id = $id ? ' id="' . esc_attr($id) . '"' : '';

		$output .= $indent . '<li' . $id . $class_names . '>';

		$attrs = array();
		$attrs['title'] = !empty($item->attr_title) ? $item->attr_title : '';
		$attrs['target'] = !empty($item->target) ? $item->target : '';
		$attrs['rel'] = !empty($item->xfn) ? $item->xfn : '';
		$attrs['href'] = !empty($item->url) ? $item->url : '';

		$attrs = apply_filters('nav_menu_link_attributes', $attrs, $item, $args, $depth);

		$attributes = '';
		foreach ($attrs as $attr => $value) {
			if (!empty($value)) {
				$value = ('href' === $attr) ? esc_url($value) : esc_attr($value);
				$attributes .= ' ' . $attr . '="' . $value . '"';
			}
		}

		$title = apply_filters('the_title', $item->title, $item->ID);

		$item_output = $args->before;
		if (in_array('menu-item-has-children', $classes)) {
			$item_output .= '<button ' . $attributes . ' class="dropdownNavbarLink flex items-center justify-between w-full py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 md:w-auto">' . $title . ' <svg class="w-2.5 h-2.5 ms-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/></svg></button>';
		} else {
			if($depth > 0) {
				$item_output .= '<a' . $attributes . ' class="block px-4 py-2 hover:bg-gray-100">' . $title . '</a>';
			} else {
				$item_output .= '<a' . $attributes . ' class="block py-2 px-3 text-gray-900 rounded hover:bg-blue-700 hover:underline md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0">' . $title . '</a>';
			}
		}
		$item_output .= $args->after;

		$output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
	}
}
