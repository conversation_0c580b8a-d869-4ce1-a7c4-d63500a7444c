<?php

namespace NeurodiversityMan\Helpers;

use NeurodiversityMan\Core\AcfFramework\BlockRegister;
use WP_Theme_JSON_Resolver;
use function is_plugin_active;

if (!defined('ABSPATH')) exit;

if (!function_exists('is_plugin_active')) {
    include_once(ABSPATH . 'wp-admin/includes/plugin.php');
}
if (is_plugin_active('advanced-custom-fields-pro/acf.php')) {
    if (!defined('ACF_PATH')) {
        define('ACF_PATH', acf_get_setting('path'));
    }
    if (!defined('ACF_URL')) {
        define('ACF_URL', acf_get_setting('url'));
    }
}

define('BlockSetup_PATH', dirname(__FILE__, 2));
define('BlockSetup_BLOCKS_PATH', BlockSetup_PATH . '/app/Blocks/');

/**
 * Initial setup for Theme ACF Blocks
 *
 * @return object BlockSetup
 * @since  1.0.0
 */
class BlockSetup
{
    private array $blockCategories;

    public function __construct()
    {
        $this->blockCategories = [
            'slug' => 'jbc-theme-blocks',
            'title' => __('JBC Theme Blocks', 'jbc'),
        ];
        if (version_compare($GLOBALS['wp_version'], '5.8-alpha-1', '<')) {
            add_filter('block_categories', [$this, 'register_category'], 10, 2);
            add_filter('block_categories', [$this, 'jbc_block_categories'], 10, 2);
        } else {
            add_filter('block_categories_all', [$this, 'register_category'], 10, 2);
            add_filter('block_categories_all', [$this, 'jbc_block_categories'], 10, 2);
        }
        add_filter('widget_categories_args', [$this, 'register_widget_category'], 10, 2);
        add_filter('acf/load_field/name=acf_editor_color_picker', [$this, '_acf_radio_color_palette']);
        add_action('admin_enqueue_scripts', [$this, 'acf_admin_style']);
    }

    function jbc_block_categories($categories, $post)
    {
        if (!is_admin() && $post->post_type !== 'page') {
            return $categories;
        }
        $jbc_block_category = $this->blockCategories;

        array_unshift($categories, $jbc_block_category);
        return $categories;
    }

    public function load(): void
    {
        if (function_exists('acf_register_block_type')) {
            $cnt = 0;
            $classArray = [];
            foreach (scandir(get_stylesheet_directory() . '/app/Blocks/') as $dir):
                if ($cnt >= 2 && $dir != '.gitkeep' && !file_exists(get_stylesheet_directory() . '/app/Blocks/'.$dir.'/block.json')):
                    $classArray[] = 'NeurodiversityMan\\Blocks\\' . str_replace(' ', '', ucwords(str_replace('-', ' ', $dir))) . '\\' . str_replace(' ', '', ucwords(str_replace('-', ' ', $dir)));
                endif;
                $cnt++;
            endforeach;

            foreach ($classArray as $class) {
                $class = new $class;
                (new BlockRegister($class))->registerBlock();
            }
        }
    }

    function acf_admin_style(): void
    {
        if (file_exists(get_stylesheet_directory_uri() . '/assets/css/acf.css')) {
            wp_enqueue_style(
                'acf-styles',
                get_stylesheet_directory_uri() . '/assets/css/acf.css',
                [],
                filemtime(get_stylesheet_directory() . '/assets/css/acf.css') // append timestamp for cache busting
            );
        }
    }

    function register_category($block_categories, $block_editor_context): array
    {
        return array_merge(
            $block_categories,
            [
                $this->blockCategories,
            ]
        );
    }

    function register_widget_category($cat_args, $instance): array
    {
        return array_merge(
            $cat_args,
            [
                $this->blockCategories,
            ]
        );
    }

    function _acf_radio_color_palette($field): array
    {

        // create color palette array
        $color_palette = [];

        // check if theme.json is being used and if so, grab the settings
        if (class_exists('WP_Theme_JSON_Resolver')) {
            $settings = WP_Theme_JSON_Resolver::get_theme_data()->get_settings();

            // full theme color palette
            if (isset($settings['color']['palette']['theme'])) {
                $color_palette = $settings['color']['palette']['theme'];
            }
        }

        // if there are colors in the $color_palette array
        if (!empty($color_palette)) {

            // loop over each color and create option
            foreach ($color_palette as $color) {
                $field['choices'][$color['slug']] = $color['name'];
            }
        }

        return $field;
    }
}

function jbc_blocks_load(): void
{
    $jbc_code = new BlockSetup();
    $jbc_code->load();
}


/**
 * Calls the function with the namespace
 */
add_action('acf/init', __NAMESPACE__ . '\jbc_blocks_load');
