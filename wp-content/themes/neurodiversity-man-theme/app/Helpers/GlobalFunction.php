<?php

namespace NeurodiversityMan\Helpers;

use stdClass;
use WP_Block_Type_Registry;

class GlobalFunction
{

    public static function get_field($selector, $post_id = FALSE, $format_value = TRUE)
    {
        if (class_exists('acf')) {
            return self::array_to_object(get_field($selector, $post_id, $format_value));
        }
    }

    public static function array_to_object($array): bool|stdClass
    {
        if (!empty($array) && is_array($array)) {
            $obj = new stdClass;
            foreach ($array as $k => $v) {
                if (strlen($k)) {
                    if (is_array($v)) {
                        $obj->{$k} = self::array_to_object($v); //RECURSION
                    } else {
                        $obj->{$k} = $v;
                    }
                }
            }
            return $obj;
        } else {
            return FALSE;
        }
    }

	public static function object_to_array($obj): bool|array
	{
		if (!empty($obj) && is_object($obj)) {
			$arr = [];
			foreach ($obj as $k => $v) {
				if (strlen($k)) {
					if (is_object($v) && $k !== 'enqueue_assets') {
						$arr[$k] = self::object_to_array($v); //RECURSION
					}
					else {
						$arr[$k] = $v;
					}
				}
			}
			return $arr;
		} else {
			return FALSE;
		}
	}

    public static function getAssetsUrl($blockName, $fileType = 'assets'): string
    {
        if (JBC_ENV === 'prod') {
            $fileType = 'min.' . $fileType;
        }
        if ($fileType == 'assets') {
            return get_stylesheet_directory_uri() . "/app/Blocks/$blockName/assets/";
        }
        if ($fileType == 'images') {
            return get_stylesheet_directory_uri() . "/app/Blocks/$blockName/assets/$fileType";
        }
        $filename = preg_replace('/(?<!^)([A-Z])/', '-\\1', $blockName);
        $filename = strtolower($filename);
        return get_stylesheet_directory_uri() . "/app/Blocks/$blockName/assets/$fileType/$filename.$fileType";
    }

    public static function __render_layout_support_flag($block_content, $block)
    {
        $block_type = WP_Block_Type_Registry::get_instance()->get_registered($block['blockName']);
        $support_layout = block_has_support($block_type, ['__experimentalLayout'], FALSE);

        if (!$support_layout) {
            return $block_content;
        }

        $block_gap = wp_get_global_settings(['spacing', 'blockGap']);
        $default_layout = wp_get_global_settings(['layout']);
        $has_block_gap_support = isset($block_gap);
        $default_block_layout = _wp_array_get($block_type->supports, ['__experimentalLayout', 'default'], []);
        $used_layout = $block['attrs']['layout'] ?? $default_block_layout;
        if (isset($used_layout['inherit']) && $used_layout['inherit']) {
            if (!$default_layout) {
                return $block_content;
            }
            $used_layout = $default_layout;
        }

        $class_name = wp_unique_id('wp-container-');
        $gap_value = _wp_array_get($block, ['attrs', 'style', 'spacing', 'blockGap']);


        if (is_array($gap_value)) {
            foreach ($gap_value as $key => $value) {
                $gap_value[$key] = $value && preg_match('%[\\\(&=}]|/\*%', $value) ? NULL : $value;
            }
        } else {
            $gap_value = $gap_value && preg_match('%[\\\(&=}]|/\*%', $gap_value) ? NULL : $gap_value;
        }

        $fallback_gap_value = _wp_array_get($block_type->supports, ['spacing', 'blockGap', '__experimentalDefault'],
            '0.5em');


        $should_skip_gap_serialization = wp_should_skip_block_supports_serialization($block_type, 'spacing',
            'blockGap');
        $style = wp_get_layout_style(".$class_name", $used_layout, $has_block_gap_support,
            $gap_value,
            $should_skip_gap_serialization, $fallback_gap_value);


        $content = preg_replace(
            '/' . preg_quote('class="', '/') . '/',
            'class="' . esc_attr($class_name) . ' ',
            $block_content,
            1
        );

        return '<style>' . $style . '</style>' . $content;
    }

	public static function launchpad_render_acf_block($block_name, $content = '', $attrs = []): string
	{
		if (!function_exists('acf_register_block_type'))
			return false;

		$block = acf_get_block_type($block_name);

		foreach ($attrs as $attr => $val) {
			$block['data'][$attr] = $val;
		}

		return acf_rendered_block($block, $content, false);
	}

	/**
	 * Get theme colors from theme.json
	 */
	public static function get_theme_colors(): array
	{
		$theme_json_path = get_template_directory() . '/theme.json';

		if (!file_exists($theme_json_path)) {
			// Fallback colors if theme.json doesn't exist
			return [
				['name' => 'Primary', 'slug' => 'primary', 'color' => '#043873'],
				['name' => 'Secondary', 'slug' => 'secondary', 'color' => '#4F9CF9'],
				['name' => 'Accent', 'slug' => 'accent', 'color' => '#FFE492'],
				['name' => 'Body Text Dark', 'slug' => 'body-text-dark', 'color' => '#212529'],
				['name' => 'Body Text Light', 'slug' => 'body-text-light', 'color' => '#ffffff'],
			];
		}

		$theme_json = json_decode(file_get_contents($theme_json_path), true);

		if (!isset($theme_json['settings']['color']['palette'])) {
			return [];
		}

		$colors = [];
		foreach ($theme_json['settings']['color']['palette'] as $color) {
			$colors[] = [
				'name' => $color['name'],
				'slug' => $color['slug'],
				'color' => trim($color['color']), // Trim any extra spaces
			];
		}

		return $colors;
	}

	/**
	 * Search theme colors by hex value and return Tailwind class
	 *
	 * @param string $hex_color The hex color to search for (with or without #)
	 * @param string $type The type of Tailwind class to return ('text', 'bg', 'border', 'ring', etc.)
	 * @param string|null $shade Optional shade for the color (e.g., '500', '600', etc.)
	 * @return string|false Returns the Tailwind class or false if color not found
	 */
	public static function get_tailwind_color_class(string $hex_color, string $type = 'text', ?string $shade = null): string|false
	{
		// Normalize the hex color
		$hex_color = strtolower(trim($hex_color));
		if (!str_starts_with($hex_color, '#')) {
			$hex_color = '#' . $hex_color;
		}

		// Get theme colors
		$theme_colors = self::get_theme_colors();

		// Search for the color
		foreach ($theme_colors as $color) {
			$theme_hex = strtolower(trim($color['color']));
			if ($theme_hex === $hex_color) {
				// Build the Tailwind class
				$class = $type . '-' . $color['slug'];

				// Add shade if provided
				if ($shade !== null) {
					$class .= '-' . $shade;
				}

				return $class;
			}
		}

		return false;
	}

	/**
	 * Get all available Tailwind color classes for a theme color
	 *
	 * @param string $hex_color The hex color to search for
	 * @param array $types Array of class types to generate (default: ['text', 'bg', 'border'])
	 * @param array $shades Array of shades to include (optional)
	 * @return array|false Returns array of Tailwind classes or false if color not found
	 */
	public static function get_all_tailwind_color_classes(string $hex_color, array $types = ['text', 'bg', 'border'], array $shades = []): array|false
	{
		// Normalize the hex color
		$hex_color = strtolower(trim($hex_color));
		if (!str_starts_with($hex_color, '#')) {
			$hex_color = '#' . $hex_color;
		}

		// Get theme colors
		$theme_colors = self::get_theme_colors();

		// Search for the color
		foreach ($theme_colors as $color) {
			$theme_hex = strtolower(trim($color['color']));
			if ($theme_hex === $hex_color) {
				$classes = [];

				foreach ($types as $type) {
					if (empty($shades)) {
						// No shades specified, return base class
						$classes[$type] = $type . '-' . $color['slug'];
					} else {
						// Generate classes with shades
						$classes[$type] = [];
						foreach ($shades as $shade) {
							$classes[$type][$shade] = $type . '-' . $color['slug'] . '-' . $shade;
						}
					}
				}

				return [
					'color_info' => $color,
					'classes' => $classes
				];
			}
		}

		return false;
	}

	/**
	 * Get theme color by slug
	 *
	 * @param string $slug The color slug (e.g., 'primary', 'secondary')
	 * @return array|false Returns color info or false if not found
	 */
	public static function get_theme_color_by_slug(string $slug): array|false
	{
		$theme_colors = self::get_theme_colors();

		foreach ($theme_colors as $color) {
			if ($color['slug'] === $slug) {
				return $color;
			}
		}

		return false;
	}

	/**
	 * Generate Tailwind class from color slug
	 *
	 * @param string $slug The color slug
	 * @param string $type The type of class ('text', 'bg', 'border', etc.)
	 * @param string|null $shade Optional shade
	 * @return string|false Returns the Tailwind class or false if slug not found
	 */
	public static function get_tailwind_class_by_slug(string $slug, string $type = 'text', ?string $shade = null): string|false
	{
		$color = self::get_theme_color_by_slug($slug);

		if (!$color) {
			return false;
		}

		$class = $type . '-' . $slug;

		if ($shade !== null) {
			$class .= '-' . $shade;
		}

		return $class;
	}
}
