<?php
/**
 * Functions which enhance the theme by adding Custom Post Type (CPT) into WordPress
 *
 * @package jbc-child-theme
 */

namespace NeurodiversityMan\Helpers;

class RegisterCpt
{
    /**
     * Post type name.
     *
     * @var string $post_type_name Holds the name of the post type.
     */
    public string $post_type_name;

    /**
     * Temporary CPT Configs.
     *
     * @var string $cptTempConf Holds temporary CPT configs.
     */
    public string $cptTempConf = '';

    /**
     * Holds the singular name of the post type. This is a human friendly
     * name, capitalized with spaces assigned on __construct().
     *
     * @var string $singular Post type singular name.
     */
    public string $singular;

    /**
     * Holds the plural name of the post type. This is a human friendly
     * name, capitalized with spaces assigned on __construct().
     *
     * @var string $plural Singular post type name.
     */
    public string $plural;

    /**
     * Post type slug. This is a robot friendly name, all lowercase and uses
     * hyphens assigned on __construct().
     *
     * @var string $slug Holds the post type slug name.
     */
    public string $slug;

    /**
     * User submitted options assigned on __construct().
     *
     * @var array $options Holds the user submitted post type options.
     */
    public array $options;

    /**
     * Taxonomies
     *
     * @var array $taxonomies Holds an array of taxonomies associated with the post type.
     */
    public array $taxonomies;

    /**
     * Taxonomy settings, an array of the taxonomies associated with the post
     * type and their options used when registering the taxonomies.
     *
     * @var array $taxonomy_settings Holds the taxonomy settings.
     */
    public array $taxonomy_settings;

    /**
     * Existing taxonomies to be registered after the post has been registered
     *
     * @var array $existing_taxonomies holds existing taxonomies
     */
    public array $existing_taxonomies = [];

    /**
     * Taxonomy filters. Defines which filters are to appear on admin edit
     * screen used in add_taxonomy_filters().
     *
     * @var array $filters Taxonomy filters.
     */
    public array $filters;

    /**
     * Defines which columns are to appear on the admin edit screen used
     * in add_admin_columns().
     *
     * @var array $columns Columns visible in admin edit screen.
     */
    public array $columns;

    /**
     * User defined functions to populate admin columns.
     *
     * @var array $custom_populate_columns User functions to populate columns.
     */
    public array $custom_populate_columns;

    /**
     * Sortable columns.
     *
     * @var array $sortable Define which columns are sortable on the admin edit screen.
     */
    public array $sortable;

    /**
     * Text-domain used for translation. Use the set_textdomain() method to set a custom textdomain.
     *
     * @var string $textdomain Used for internationalising. Defaults to "cpt" without quotes.
     */
    public string $textdomain = 'cpt';

    /**
     * Textdomain used for translation. Use the set_textdomain() method to set a custom textdomain.
     *
     * @var string $textdomain Used for internationalising. Defaults to "cpt" without quotes.
     */
    public string $cptTerm = 'cpt';

    /**
     * Constructor
     *
     * Register a custom post type.
     *
     */

    function __construct($init = FALSE)
    {
        if ($init) {
            $cnt = 0;
            $classArray = [];
            foreach (scandir(get_stylesheet_directory() . '/app/CPT/') as $dir):
                if ($cnt >= 2 && $dir != '.gitkeep'):
                    $classArray[] = 'NeurodiversityMan\CPT\\' . str_replace('.php', '', $dir);
                endif;
                $cnt++;
            endforeach;

            foreach ($classArray as $class) {
                new $class;
            }
        }
    }

    /**
     * registerCpt
     *
     * Register a custom post type.
     *
     * @param mixed $post_type_names The name(s) of the post type, accepts (post type name, slug, plural, singular).
     * @param array $options User submitted options.
     */
    function registerCpt(mixed $post_type_names, array $options = []): void
    {

        if (is_array($post_type_names)) {

            $names = [
                'singular',
                'plural',
                'slug',
            ];
            $this->post_type_name = $post_type_names['post_type_name'];
            $this->options = $post_type_names;
            foreach ($names as $name) {

                if (isset($post_type_names[$name])) {

                    $this->$name = $post_type_names[$name];

                } else {

                    $method = 'get_' . $name;

                    $this->$name = $this->$method();
                }
            }

        } else {

            $this->post_type_name = $post_type_names;

            $this->slug = $options['slug'] ?? $this->get_slug();

            $this->plural = $options['plural'] ?? $this->get_plural();

            $this->singular = $options['singular'] ?? $this->get_singular();

            $this->options = $options;
        }


        $this->add_action('init', [&$this, 'register_taxonomies']);

        $this->add_action('init', [&$this, 'register_post_type']);

        $this->add_action('init', [&$this, 'register_existing_taxonomies']);

        $this->add_filter('manage_edit-' . $this->post_type_name . '_columns', [&$this, 'add_admin_columns']);

        $this->add_action('manage_' . $this->post_type_name . '_posts_custom_column',
            [&$this, 'populate_admin_columns'], 10, 2);

        $this->add_action('restrict_manage_posts', [&$this, 'add_taxonomy_filters']);

        $this->add_filter('post_updated_messages', [&$this, 'updated_messages']);
        $this->add_filter('bulk_post_updated_messages', [&$this, 'bulk_updated_messages'], 10, 2);
    }

    /**
     * Get slug
     *
     * Creates an url friendly slug.
     *
     * @param string|null $name Name to slugify.
     *
     * @return string $name Returns the slug.
     */
    function get_slug(string $name = NULL): string
    {

        // If no name set use the post type name.
        if (!isset($name)) {

            $name = $this->post_type_name;
        }

        // Name to lower case.
        $name = strtolower(Inflect::singularize($name));

        // Replace spaces with hyphen.
        $name = str_replace(" ", "-", $name);

        // Replace underscore with hyphen.
        return str_replace("_", "-", $name);
    }

    /**
     * Get plural
     *
     * Returns the friendly plural name.
     *
     *    ucwords      capitalize words
     *    strtolower   makes string lowercase before capitalizing
     *    str_replace  replace all instances of _ to space
     *
     * @param string|null $name The slug name you want to pluralize.
     *
     * @return string the friendly pluralized name.
     */
    function get_plural(string $name = NULL): string
    {

        // If no name is passed the post_type_name is used.
        if (!isset($name)) {

            $name = $this->post_type_name;
        }

        // Return the plural name. Add 's' to the end.
        return $this->get_human_friendly(Inflect::pluralize($name));
    }

    /**
     * Get human friendly
     *
     * Returns the human friendly name.
     *
     *    ucwords      capitalize words
     *    strtolower   makes string lowercase before capitalizing
     *    str_replace  replace all instances of hyphens and underscores to spaces
     *
     * @param string|null $name The name you want to make friendly.
     *
     * @return string The human friendly name.
     */
    function get_human_friendly(string $name = NULL): string
    {

        // If no name is passed the post_type_name is used.
        if (!isset($name)) {

            $name = $this->post_type_name;
        }

        // Return human friendly name.
        return ucwords(strtolower(str_replace("-", " ", str_replace("_", " ", $name))));
    }

    /**
     * Get singular
     *
     * Returns the friendly singular name.
     *
     *    ucwords      capitalize words
     *    strtolower   makes string lowercase before capitalizing
     *    str_replace  replace all instances of _ to space
     *
     * @param string|null $name The slug name you want to unpluralize.
     *
     * @return string The friendly singular name.
     */
    function get_singular(string $name = NULL): string
    {

        // If no name is passed the post_type_name is used.
        if (!isset($name)) {

            $name = $this->post_type_name;

        }

        // Return the string.
        return $this->get_human_friendly(Inflect::singularize($name));
    }

    /**
     * Add Action
     *
     * Helper function to add add_action WordPress filters.
     *
     * @param string $action Name of the action.
     * @param array $function Function to hook that will run on action.
     * @param integer $priority Order in which to execute the function, relation to other functions hooked to this action.
     * @param integer $accepted_args The number of arguments the function accepts.
     */
    function add_action(string $action, array $function, int $priority = 10, int $accepted_args = 1): void
    {
        // Pass variables into WordPress add_action function
        add_action($action, $function, $priority, $accepted_args);
    }

    /**
     * Add Filter
     *
     * Create add_filter WordPress filter.
     *
     * @see http://codex.wordpress.org/Function_Reference/add_filter
     *
     * @param string $action Name of the action to hook to, e.g 'init'.
     * @param array $function Function to hook that will run on @action.
     * @param int $priority Order in which to execute the function, relation to other function hooked to this action.
     * @param int $accepted_args The number of arguements the function accepts.
     */
    function add_filter(string $action, array $function, int $priority = 10, int $accepted_args = 1): void
    {

        // Pass variables into WordPress add_action function
        add_filter($action, $function, $priority, $accepted_args);
    }

    /**
     * Get
     *
     * Helper function to get an object variable.
     *
     * @param string $var The variable you would like to retrieve.
     *
     * @return mixed Returns the value on success, boolean false whe it fails.
     */
    function get(string $var): mixed
    {
        // If the variable exists.
        if ($this->$var) {

            // On success return the value.
            return $this->$var;

        } else {

            // on fail return false
            return FALSE;
        }
    }

    /**
     * Set
     *
     * Helper function used to set an object variable. Can overwrite existing
     * variables or create new ones. Cannot overwrite reserved variables.
     *
     * @param mixed $var The variable you would like to create/overwrite.
     * @param mixed $value The value you would like to set to the variable.
     */
    function set(mixed $var, mixed $value): void
    {
        // An array of reserved variables that cannot be overwritten.
        $reserved = [
            'config',
            'post_type_name',
            'singular',
            'plural',
            'slug',
            'options',
            'taxonomies',
        ];

        // If the variable is not a reserved variable
        if (!in_array($var, $reserved)) {

            // Write variable and value
            $this->$var = $value;
        }
    }

    /**
     * Register Post Type
     *
     * @see http://codex.wordpress.org/Function_Reference/register_post_type
     */
    function register_post_type(): void
    {

        // Friendly post type names.
        $plural = $this->plural;
        $singular = $this->singular;
        $slug = $this->slug;

        // Default labels.
        $labels = [
            'name' => sprintf(__('%s', $this->textdomain), $plural),
            'singular_name' => sprintf(__('%s', $this->textdomain), $singular),
            'menu_name' => sprintf(__('%s', $this->textdomain), $plural),
            'all_items' => sprintf(__('%s', $this->textdomain), $plural),
            'add_new' => __('Add New', $this->textdomain),
            'add_new_item' => sprintf(__('Add New %s', $this->textdomain), $singular),
            'edit_item' => sprintf(__('Edit %s', $this->textdomain), $singular),
            'new_item' => sprintf(__('New %s', $this->textdomain), $singular),
            'view_item' => sprintf(__('View %s', $this->textdomain), $singular),
            'search_items' => sprintf(__('Search %s', $this->textdomain), $plural),
            'not_found' => sprintf(__('No %s found', $this->textdomain), $plural),
            'not_found_in_trash' => sprintf(__('No %s found in Trash', $this->textdomain), $plural),
            'parent_item_colon' => sprintf(__('Parent %s:', $this->textdomain), $singular),
        ];
        $support = '';
        if (isset($this->options['supports'])) {
            $support = $this->options['supports'];
        } else {
            $support = [
                'title',
                'editor',
                'excerpt',
                'author',
                'thumbnail',
                'revisions',
                'custom-fields',
                'category',
            ];
        }
        // Default options.
        $defaults = [
            'labels' => $labels,
            'supports' => $support,
            'hierarchical' => FALSE,
            'public' => TRUE,
            'show_ui' => TRUE,
            'show_in_menu' => TRUE,
            'show_in_nav_menus' => TRUE,
            'show_in_admin_bar' => TRUE,
            'menu_position' => 5,
            'can_export' => TRUE,
            'has_archive' => TRUE,
            'exclude_from_search' => FALSE,
            'publicly_queryable' => TRUE,
            'capability_type' => 'post',
            'show_in_rest' => TRUE,
            'rewrite' => [
                'slug' => $slug,
            ],
        ];

        // Merge user submitted options with defaults.
        $options = array_replace_recursive($defaults, $this->options);

        // Set the object options as full options passed.
        $this->options = $options;

        // Check that the post type doesn't already exist.
        if (!post_type_exists($this->post_type_name)) {

            // Register the post type.
            register_post_type($this->post_type_name, $options);
            if ($this->post_type_name != NULL && $this->cptTempConf) {
                $page_type_object = get_post_type_object($this->post_type_name);
                $page_type_object->template = [$this->cptTempConf];
            }
        }
    }

    /**
     * Register taxonomy
     *
     * @see http://codex.wordpress.org/Function_Reference/register_taxonomy
     *
     * @param $taxonomy_names
     * @param array $options Taxonomy options.
     * @param array $terms Taxonomy options.
     */
    function register_taxonomy($taxonomy_names, array $options = [], array $terms = []): void
    {
        // Post type defaults to $this post type if unspecified.
        $post_type = $this->post_type_name;

        // An array of the names required excluding taxonomy_name.
        $names = [
            'singular',
            'plural',
            'slug',
        ];

        // if an array of names is passed
        if (!empty($options)) {

            // Cycle through possible names.
            foreach ($names as $name) {
                $taxonomy_name = $taxonomy_names;
                // If the user has set the name.
                if (isset($options[$name])) {

                    // Use user submitted name.
                    $$name = $options[$name];

                    // Else generate the name.
                } else {

                    // Define the function to be used.
                    $method = 'get_' . $name;

                    // Generate the name
                    $$name = $this->$method($taxonomy_name);

                }
            }

            // Else if only the taxonomy_name has been supplied.
        } else {

            // Create user-friendly names.
            $taxonomy_name = $taxonomy_names;
            $singular = $this->get_singular($taxonomy_name);
            $plural = $this->get_plural($taxonomy_name);
            $slug = $this->get_slug($taxonomy_name);

        }

        // Default labels.
        $labels = [
            'name' => sprintf(__('%s', $this->textdomain), $plural),
            'singular_name' => sprintf(__('%s', $this->textdomain), $singular),
            'menu_name' => sprintf(__('%s', $this->textdomain), $plural),
            'all_items' => sprintf(__('All %s', $this->textdomain), $plural),
            'edit_item' => sprintf(__('Edit %s', $this->textdomain), $singular),
            'view_item' => sprintf(__('View %s', $this->textdomain), $singular),
            'update_item' => sprintf(__('Update %s', $this->textdomain), $singular),
            'add_new_item' => sprintf(__('Add New %s', $this->textdomain), $singular),
            'new_item_name' => sprintf(__('New %s Name', $this->textdomain), $singular),
            'parent_item' => sprintf(__('Parent %s', $this->textdomain), $plural),
            'parent_item_colon' => sprintf(__('Parent %s:', $this->textdomain), $plural),
            'search_items' => sprintf(__('Search %s', $this->textdomain), $plural),
            'popular_items' => sprintf(__('Popular %s', $this->textdomain), $plural),
            'separate_items_with_commas' => sprintf(__('Seperate %s with commas', $this->textdomain), $plural),
            'add_or_remove_items' => sprintf(__('Add or remove %s', $this->textdomain), $plural),
            'choose_from_most_used' => sprintf(__('Choose from most used %s', $this->textdomain), $plural),
            'not_found' => sprintf(__('No %s found', $this->textdomain), $plural),
        ];

        // Default options.
        $defaults = [
            'labels' => $labels,
            'hierarchical' => TRUE,
            'show_in_rest' => TRUE,
            'rewrite' => [
                'slug' => $slug,
            ],
        ];

        // Merge default options with user submitted options.
        $options = array_replace_recursive($defaults, $options);

        // Add the taxonomy to the object array, this is used to add columns and filters to admin panel.
        $this->taxonomies[] = $taxonomy_name;

        // Create array used when registering taxonomies.

        $this->taxonomy_settings[$taxonomy_name] = $options;
        $this->taxonomy_settings[$taxonomy_name]['terms'] = $terms;

    }


    /**
     * Register taxonomies
     *
     * Cycles through taxonomies added with the class and registers them.
     */
    function register_taxonomies(): void
    {

        if (!empty($this->taxonomy_settings) && is_array($this->taxonomy_settings)) {

            // Foreach taxonomy registered with the post type.
            foreach ($this->taxonomy_settings as $taxonomy_name => $options) {

                // Register the taxonomy if it doesn't exist.
                if (!taxonomy_exists($taxonomy_name)) {

                    // Register the taxonomy with WordPress
                    register_taxonomy($taxonomy_name, $this->post_type_name, $options);
                    $this->register_terms($taxonomy_name, $this->taxonomy_settings[$taxonomy_name]['terms']);

                } else {

                    // If taxonomy exists, register it later with register_existing_taxonomies
                    $this->existing_taxonomies[] = $taxonomy_name;
                    $this->register_terms($taxonomy_name, $this->taxonomy_settings[$taxonomy_name]['terms']);
                }
            }
        }
    }

    function register_terms($taxonomy, $terms): void
    {
        foreach ($terms as $term) {
            $cat_exists = term_exists(strtolower(str_replace(' ', '-', $term)), $taxonomy);
            $new_cat = NULL;
            if (!$cat_exists) {
                // if term is not exist, insert it
                $insert = wp_insert_term(
                    $term,
                    $taxonomy,
                    [
                        'description' => 'This is a default category',
                        'slug' => strtolower(str_replace(' ', '-', $term)),
                    ]
                );
            }
        }
    }

    /**
     * Register Exisiting Taxonomies
     *
     * Cycles through exisiting taxonomies and registers them after the post type has been registered
     */
    function register_existing_taxonomies(): void
    {

        if (is_array($this->existing_taxonomies)) {
            foreach ($this->existing_taxonomies as $taxonomy_name) {
                register_taxonomy_for_object_type($taxonomy_name, $this->post_type_name);
            }
        }
    }

    /**
     * Add admin columns
     *
     * Adds columns to the admin edit screen. Function is used with add_action
     *
     * @param array $columns Columns to be added to the admin edit screen.
     *
     * @return array
     */
    function add_admin_columns(array $columns): array
    {

        // If no user columns have been specified, add taxonomies
        if (!isset($this->columns)) {

            $new_columns = [];

            // determine which column to add custom taxonomies after
            if (!empty($this->taxonomies) && is_array($this->taxonomies)
                && in_array('post_tag', $this->taxonomies)
                || $this->post_type_name === 'post') {
                $after = 'tags';
            } elseif (!empty($this->taxonomies) && is_array($this->taxonomies)
                && in_array('category', $this->taxonomies)
                || $this->post_type_name === 'post') {
                $after = 'categories';
            } elseif (post_type_supports($this->post_type_name, 'author')) {
                $after = 'author';
            } else {
                $after = 'title';
            }

            // foreach exisiting columns
            foreach ($columns as $key => $title) {

                // add exisiting column to the new column array
                $new_columns[$key] = $title;

                // we want to add taxonomy columns after a specific column
                if ($key === $after) {

                    // If there are taxonomies registered to the post type.
                    if (!empty($this->taxonomies) && is_array($this->taxonomies)) {

                        // Create a column for each taxonomy.
                        foreach ($this->taxonomies as $tax) {

                            // WordPress adds Categories and Tags automatically, ignore these
                            if ($tax !== 'category' && $tax !== 'post_tag') {
                                // Get the taxonomy object for labels.
                                $taxonomy_object = get_taxonomy($tax);

                                // Column key is the slug, value is friendly name.
                                $new_columns[$tax] = sprintf(__('%s', $this->textdomain),
                                    $taxonomy_object->labels->name);
                            }
                        }
                    }
                }
            }

            // override with new columns
            $columns = $new_columns;

        } else {

            // Use user submitted columns, these are defined using the object columns() method.
            $columns = $this->columns;
        }

        return $columns;
    }

    /**
     * Populate admin columns
     *
     * Populate custom columns on the admin edit screen.
     *
     * @param string $column The name of the column.
     * @param integer $post_id The post ID.
     */
    function populate_admin_columns(string $column, int $post_id): void
    {

        // Get wordpress $post object.
        global $post;

        // determine the column
        switch ($column) {

            // If column is a taxonomy associated with the post type.
            case (taxonomy_exists($column)) :

                // Get the taxonomy for the post
                $terms = get_the_terms($post_id, $column);

                // If we have terms.
                if (!empty($terms)) {

                    $output = [];

                    // Loop through each term, linking to the 'edit posts' page for the specific term.
                    foreach ($terms as $term) {

                        // Output is an array of terms associated with the post.
                        $output[] = sprintf(

                        // Define link.
                            '<a href="%s">%s</a>',

                            // Create filter url.
                            esc_url(add_query_arg(['post_type' => $post->post_type, $column => $term->slug],
                                'edit.php')),

                            // Create friendly term name.
                            esc_html(sanitize_term_field('name', $term->name, $term->term_id, $column, 'display'))
                        );

                    }

                    // Join the terms, separating them with a comma.
                    echo join(', ', $output);

                    // If no terms found.
                } else {

                    // Get the taxonomy object for labels
                    $taxonomy_object = get_taxonomy($column);

                    // Echo no terms.
                    printf(__('No %s', $this->textdomain), $taxonomy_object->labels->name);
                }

                break;

            // If column is for the post ID.
            case 'post_id' :

                echo $post->ID;

                break;

            // if the column is prepended with 'meta_', this will automagically retrieve the meta values and display them.
            case (preg_match('/^meta_/', $column) ? TRUE : FALSE) :

                // meta_book_author (meta key = book_author)
                $x = substr($column, 5);

                $meta = get_post_meta($post->ID, $x);

                echo join(", ", $meta);

                break;

            // If the column is post thumbnail.
            case 'icon' :

                // Create the edit link.
                $link = esc_url(add_query_arg(['post' => $post->ID, 'action' => 'edit'], 'post.php'));

                // If it post has a featured image.
                if (has_post_thumbnail()) {

                    // Display post featured image with edit link.
                    echo '<a href="' . $link . '">';
                    the_post_thumbnail([60, 60]);
                    echo '</a>';

                } else {

                    // Display default media image with link.
                    echo '<a href="' . $link . '"><img src="' . site_url('/wp-includes/images/crystal/default.png') . '" alt="' . $post->post_title . '" /></a>';

                }

                break;

            // Default case checks if the column has a user function, this is most commonly used for custom fields.
            default :

                // If there are user custom columns to populate.
                if (isset($this->custom_populate_columns) && is_array($this->custom_populate_columns)) {

                    // If this column has a user submitted function to run.
                    if (isset($this->custom_populate_columns[$column]) && is_callable($this->custom_populate_columns[$column])) {

                        // Run the function.
                        call_user_func_array($this->custom_populate_columns[$column], [$column, $post]);

                    }
                }

                break;
        } // end switch( $column )
    }

    /**
     * Filters
     *
     * User function to define which taxonomy filters to display on the admin page.
     *
     * @param array $filters An array of taxonomy filters to display.
     */
    function filters(array $filters = []): void
    {

        $this->filters = $filters;
    }

    /**
     *  Add taxtonomy filters
     *
     * Creates select fields for filtering posts by taxonomies on admin edit screen.
     */
    function add_taxonomy_filters(): void
    {

        global $typenow;
        global $wp_query;

        // Must set this to the post type you want the filter(s) displayed on.
        if ($typenow == $this->post_type_name) {

            // if custom filters are defined use those
            if (!empty($this->filters) && is_array($this->filters)) {

                $filters = $this->filters;

                // else default to use all taxonomies associated with the post
            } else {
                if (!empty($this->taxonomies)) {
                    $filters = $this->taxonomies;
                }
            }

            if (!empty($filters)) {

                // Foreach of the taxonomies we want to create filters for...
                foreach ($filters as $tax_slug) {

                    // ...object for taxonomy, doesn't contain the terms.
                    $tax = get_taxonomy($tax_slug);

                    // Get taxonomy terms and order by name.
                    $args = [
                        'orderby' => 'name',
                        'hide_empty' => FALSE,
                    ];

                    // Get taxonomy terms.
                    $terms = get_terms($tax_slug, $args);

                    // If we have terms.
                    if ($terms) {

                        // Set up select box.
                        printf(' &nbsp;<select name="%s" class="postform">', $tax_slug);

                        // Default show all.
                        printf('<option value="0">%s</option>',
                            sprintf(__('Show all %s', $this->textdomain), $tax->label));

                        // Foreach term create an option field...
                        foreach ($terms as $term) {

                            // ...if filtered by this term make it selected.
                            if (isset($_GET[$tax_slug]) && $_GET[$tax_slug] === $term->slug) {

                                printf('<option value="%s" selected="selected">%s (%s)</option>', $term->slug,
                                    $term->name, $term->count);

                                // ...create option for taxonomy.
                            } else {

                                printf('<option value="%s">%s (%s)</option>', $term->slug, $term->name, $term->count);
                            }
                        }
                        // End the select field.
                        print('</select>&nbsp;');
                    }
                }
            }
        }
    }

    /**
     * Columns
     *
     * Choose columns to be displayed on the admin edit screen.
     *
     * @param array $columns An array of columns to be displayed.
     */
    function columns(array $columns): void
    {

        // If columns is set.
        if (!empty($columns)) {

            // Assign user submitted columns to object.
            $this->columns = $columns;

        }
    }

    /**
     * Populate columns
     *
     * Define what and how to populate a speicific admin column.
     *
     * @param string $column_name The name of the column to populate.
     * @param mixed $callback An anonyous function or callable array to call when populating the column.
     */
    function populate_column(string $column_name, mixed $callback): void
    {
        $this->custom_populate_columns[$column_name] = $callback;
    }

    /**
     * Sortable
     *
     * Define what columns are sortable in the admin edit screen.
     *
     * @param array $columns An array of columns that are sortable.
     */
    function sortable(array $columns = []): void
    {

        // Assign user defined sortable columns to object variable.
        $this->sortable = $columns;

        // Run filter to make columns sortable.
        $this->add_filter('manage_edit-' . $this->post_type_name . '_sortable_columns',
            [&$this, 'make_columns_sortable']);

        // Run action that sorts columns on request.
        $this->add_action('load-edit.php', [&$this, 'load_edit']);
    }

    /**
     * Make columns sortable
     *
     * Internal function that adds user defined sortable columns to WordPress default columns.
     *
     * @param array $columns Columns to be sortable.
     *
     */
    function make_columns_sortable(array $columns): array
    {

        // For each sortable column.
        foreach ($this->sortable as $column => $values) {

            // Make an array to merge into WordPress sortable columns.
            $sortable_columns[$column] = $values[0];
        }

        // Merge sortable columns array into WordPress sortable columns.
        return array_merge($sortable_columns, $columns);
    }

    /**
     * Load edit
     *
     * Sort columns only on the edit.php page when requested.
     *
     * @see http://codex.wordpress.org/Plugin_API/Filter_Reference/request
     */
    function load_edit(): void
    {

        // Run filter to sort columns when requested
        $this->add_filter('request', [&$this, 'sort_columns']);

    }

    /**
     * Sort columns
     *
     * Internal function that sorts columns on request.
     *
     * @param array $vars The query vars submitted by user.
     *
     * @return array A sorted array.
     * @see load_edit()
     *
     */
    function sort_columns(array $vars): array
    {

        // Cycle through all sortable columns submitted by the user
        foreach ($this->sortable as $column => $values) {

            // Retrieve the meta key from the user submitted array of sortable columns
            $meta_key = $values[0];

            // If the meta_key is a taxonomy
            if (taxonomy_exists($meta_key)) {

                // Sort by taxonomy.
                $key = "taxonomy";

            } else {

                // else by meta key.
                $key = "meta_key";
            }

            // If the optional parameter is set and is set to true
            if (isset($values[1]) && TRUE === $values[1]) {

                // Vaules needed to be ordered by integer value
                $orderby = 'meta_value_num';

            } else {

                // Values are to be order by string value
                $orderby = 'meta_value';
            }

            // Check if we're viewing this post type
            if (isset($vars['post_type']) && $this->post_type_name == $vars['post_type']) {

                // find the meta key we want to order posts by
                if (isset($vars['orderby']) && $meta_key == $vars['orderby']) {

                    // Merge the query vars with our custom variables
                    $vars = array_merge(
                        $vars,
                        [
                            'meta_key' => $meta_key,
                            'orderby' => $orderby,
                        ]
                    );
                }
            }
        }

        return $vars;
    }

    /**
     * Set menu icon
     *
     * Use this function to set the menu icon in the admin dashboard. Since WordPress v3.8
     * dashicons are used. For more information see @link http://melchoyce.github.io/dashicons/
     *
     * @param string $icon dashicon name
     *
     * @example dashicons-welcome-write-blog
     *
     */
    function menu_icon(string $icon = "dashicons-admin-page"): void
    {

        if (is_string($icon) && stripos($icon, "dashicons") !== FALSE) {

            $this->options["menu_icon"] = $icon;

        } else {

            // Set a default menu icon
            $this->options["menu_icon"] = "dashicons-admin-page";
        }
    }

    /**
     * Set textdomain
     *
     * @param string $textdomain Textdomain used for translation.
     */
    function set_textdomain($textdomain): void
    {
        $this->textdomain = $textdomain;
    }

    /**
     * Updated messages
     *
     * Internal function that modifies the post type names in updated messages
     *
     * @param array $messages an array of post updated messages
     */
    function updated_messages(array $messages): array
    {

        $post = get_post();
        $singular = $this->singular;

        $messages[$this->post_type_name] = [
            0 => '',
            1 => sprintf(__('%s updated.', $this->textdomain), $singular),
            2 => __('Custom field updated.', $this->textdomain),
            3 => __('Custom field deleted.', $this->textdomain),
            4 => sprintf(__('%s updated.', $this->textdomain), $singular),
            5 => isset($_GET['revision']) ? sprintf(__('%2$s restored to revision from %1$s', $this->textdomain),
                wp_post_revision_title((int)$_GET['revision'], FALSE),
                $singular) : FALSE,
            6 => sprintf(__('%s updated.', $this->textdomain), $singular),
            7 => sprintf(__('%s saved.', $this->textdomain), $singular),
            8 => sprintf(__('%s submitted.', $this->textdomain), $singular),
            9 => sprintf(
                __('%2$s scheduled for: <strong>%1$s</strong>.', $this->textdomain),
                date_i18n(__('M j, Y @ G:i', $this->textdomain), strtotime($post->post_date)),
                $singular
            ),
            10 => sprintf(__('%s draft updated.', $this->textdomain), $singular),
        ];

        return $messages;
    }

    /**
     * Bulk updated messages
     *
     * Internal function that modifies the post type names in bulk updated messages
     *
     * @param $bulk_messages
     * @param $bulk_counts
     *
     * @return mixed
     */
    function bulk_updated_messages($bulk_messages, $bulk_counts): mixed
    {

        $singular = $this->singular;
        $plural = $this->plural;

        $bulk_messages[$this->post_type_name] = [
            'updated' => _n('%s ' . $singular . ' updated.', '%s ' . $plural . ' updated.',
                $bulk_counts['updated']),
            'locked' => _n('%s ' . $singular . ' not updated, somebody is editing it.',
                '%s ' . $plural . ' not updated, somebody is editing them.', $bulk_counts['locked']),
            'deleted' => _n('%s ' . $singular . ' permanently deleted.', '%s ' . $plural . ' permanently deleted.',
                $bulk_counts['deleted']),
            'trashed' => _n('%s ' . $singular . ' moved to the Trash.', '%s ' . $plural . ' moved to the Trash.',
                $bulk_counts['trashed']),
            'untrashed' => _n('%s ' . $singular . ' restored from the Trash.',
                '%s ' . $plural . ' restored from the Trash.', $bulk_counts['untrashed']),
        ];

        return $bulk_messages;
    }

    /**
     * Flush
     *
     * Flush rewrite rules programmatically
     */
    function flush(): void
    {
        flush_rewrite_rules();
    }
}
