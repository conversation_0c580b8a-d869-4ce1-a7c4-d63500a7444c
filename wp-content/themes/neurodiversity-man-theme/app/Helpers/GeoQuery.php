<?php

namespace NeurodiversityMan\Helpers;

if (!defined('ABSPATH')) exit;

class GeoQuery
{
    private function __construct()
    {
        add_filter('posts_fields', [$this, 'posts_fields'], 10, 2);
        add_filter('posts_join', [$this, 'posts_join'], 10, 2);
        add_filter('posts_where', [$this, 'posts_where'], 10, 2);
        add_filter('posts_orderBy', [$this, 'posts_orderBy'], 10, 2);
    }

    public static function Instance()
    {
        static $instance = NULL;
        if ($instance === NULL) {
            $instance = new self();
        }
        return $instance;
    }

    // add a calculated "distance" parameter to the sql query, using a haversine formula

    public static function the_distance($post_obj = NULL, $round = FALSE)
    {
        echo self::get_the_distance($post_obj, $round);
    }

    public static function get_the_distance($post_obj = NULL, $round = FALSE)
    {
        global $post;
        if (!$post_obj) {
            $post_obj = $post;
        }
        if (property_exists($post_obj, 'geo_query_distance')) {
            $distance = $post_obj->geo_query_distance;
            if ($round !== FALSE) {
                $distance = round($distance, $round);
            }
            return $distance;
        }
        return FALSE;
    }

    // match on the right metafields, and filter by distance

    public static function getDistance($latitudeFrom, $longitudeFrom, $latitudeTo, $longitudeTo, $earthRadius = 6371000): float|int
    {
        // convert from degrees to radians
        $latFrom = deg2rad($latitudeFrom);
        $lonFrom = deg2rad($longitudeFrom);
        $latTo = deg2rad($latitudeTo);
        $lonTo = deg2rad($longitudeTo);

        $lonDelta = $lonTo - $lonFrom;
        $a = pow(cos($latTo) * sin($lonDelta), 2) +
            pow(cos($latFrom) * sin($latTo) - sin($latFrom) * cos($latTo) * cos($lonDelta), 2);
        $b = sin($latFrom) * sin($latTo) + cos($latFrom) * cos($latTo) * cos($lonDelta);

        $angle = atan2(sqrt($a), $b);
        return $angle * $earthRadius;
    }

    // handle ordering

    public static function geoLocate($address)
    {
        try {
            $lat = 0;
            $lng = 0;

            $data_location = "https://maps.google.com/maps/api/geocode/json?key=" . get_option('jbc_options')['jbc_google_key'] . "&address=" . str_replace(" ", "+",
                    $address);
            $data = file_get_contents($data_location);

            usleep(200000);
            // turn this on to see if we are being blocked
            // echo $data;
            $data = json_decode($data);
            if ($data->status == "OK") {
                $data_location = "https://maps.google.com/maps/api/geocode/json?key=" . get_option('jbc_options')['jbc_google_key'] . "&address=" . str_replace(" ", "+",
                        $address) . "&components=country:GB";
            }
            $data = file_get_contents($data_location);

            usleep(200000);
            // turn this on to see if we are being blocked
            // echo $data;
            $data = json_decode($data);
            if ($data->status == "OK") {
                $lat = $data->results[0]->geometry->location->lat;
                $lng = $data->results[0]->geometry->location->lng;

                if ($lat && $lng) {
                    return [
                        'status' => TRUE,
                        'lat' => $lat,
                        'long' => $lng,
                        'google_place_id' => $data->results[0]->place_id,
                    ];
                }
            }
            if ($data->status == 'OVER_QUERY_LIMIT') {
                return [
                    'status' => FALSE,
                    'message' => 'Google Amp API OVER_QUERY_LIMIT, Please update your google map api key or try tomorrow',
                ];
            }

        } catch (Exception $e) {

        }

        return ['lat' => NULL, 'long' => NULL, 'status' => FALSE];
    }

    public function posts_fields($sql, $query)
    {
        global $wpdb;
        $geo_query = $query->get('geo_query');
        if ($geo_query) {

            if ($sql) {
                $sql .= ', ';
            }
            $sql .= $this->haversine_term($geo_query) . " AS geo_query_distance";
        }
        return $sql;
    }

    private function haversine_term($geo_query)
    {
        global $wpdb;
        $units = "miles";
        if (!empty($geo_query['units'])) {
            $units = strtolower($geo_query['units']);
        }
        $radius = 3959;
        if (in_array($units, ['km', 'kilometers'])) {
            $radius = 6371;
        }
        $lat_field = "geo_query_lat.meta_value";
        $lng_field = "geo_query_lng.meta_value";
        $lat = 0;
        $lng = 0;
        if (isset($geo_query['latitude'])) {
            $lat = $geo_query['latitude'];
        }
        if (isset($geo_query['longitude'])) {
            $lng = $geo_query['longitude'];
        }
        $haversine = "( " . $radius . " * ";
        $haversine .= "acos( cos( radians(%f) ) * cos( radians( " . $lat_field . " ) ) * ";
        $haversine .= "cos( radians( " . $lng_field . " ) - radians(%f) ) + ";
        $haversine .= "sin( radians(%f) ) * sin( radians( " . $lat_field . " ) ) ) ";
        $haversine .= ")";

        return $wpdb->prepare($haversine, [$lat, $lng, $lat]);
    }

    public function posts_join($sql, $query)
    {
        global $wpdb;
        $geo_query = $query->get('geo_query');
        if ($geo_query) {

            if ($sql) {
                $sql .= ' ';
            }
            $sql .= "INNER JOIN " . $wpdb->prefix . "postmeta AS geo_query_lat ON ( " . $wpdb->prefix . "posts.ID = geo_query_lat.post_id ) ";
            $sql .= "INNER JOIN " . $wpdb->prefix . "postmeta AS geo_query_lng ON ( " . $wpdb->prefix . "posts.ID = geo_query_lng.post_id ) ";
        }
        return $sql;
    }

    public function posts_where($sql, $query)
    {
        global $wpdb;
        $geo_query = $query->get('geo_query');
        if ($geo_query) {
            $lat_field = 'latitude';
            if (!empty($geo_query['lat_field'])) {
                $lat_field = $geo_query['lat_field'];
//                 print_r($lat_field);
//                 die;
            }
            $lng_field = 'longitude';
            if (!empty($geo_query['lng_field'])) {
                $lng_field = $geo_query['lng_field'];
            }
            if (isset($geo_query['distance'])) {
                $distance = $geo_query['distance'];
//                     print_r($distance);
//                     die;
            }
            if ($sql) {
                $sql .= " AND ";
            }
            $haversine = $this->haversine_term($geo_query);
            $new_sql = "( geo_query_lat.meta_key = %s AND geo_query_lng.meta_key = %s AND " . $haversine . " <= %f )";
            $sql .= $wpdb->prepare($new_sql, $lat_field, $lng_field, $distance);
        }
        return $sql;
    }

    public function posts_orderBy($sql, $query)
    {
        $geo_query = $query->get('geo_query');
        if ($geo_query) {
            $orderBy = $query->get('orderby');
            $order = $query->get('order');
            if ($orderBy == 'distance') {
                if (!$order) {
                    $order = 'ASC';
                }
                $sql = 'geo_query_distance ' . $order;
            }
        }
        return $sql;
    }
}


GeoQuery::Instance();
