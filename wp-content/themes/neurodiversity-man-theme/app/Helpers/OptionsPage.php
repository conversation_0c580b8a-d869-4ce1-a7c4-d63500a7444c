<?php

namespace NeurodiversityMan\Helpers;

use NeurodiversityMan\Core\AcfFramework\FieldsBuilder;

class OptionsPage
{
	private static mixed $theme_options;
	protected array $config = [];

	public function __construct()
	{
		add_action('admin_menu', array($this, 'add_global_options_page'));
		add_action( 'admin_bar_menu', [ $this, 'add_custom_admin_bar_link' ], 999 );
		acf_add_local_field_group(self::registerFields());
		add_action('admin_head', [$this, 'optionPageStyles']);
		if ( is_plugin_active( 'formidable/formidable.php' ) ) {
			add_filter( 'acf/load_field/name=forms', [ $this, 'populate_formidable_forms_choices' ] );
		}
	}

	public function optionPageStyles()
	{
		?>
			<style>
				.acf-field-global-settings-global-settings {
					padding:0 !important;
				}

				.acf-field-global-settings-global-settings .acf-fields > .acf-field.acf-field-group {
					padding:0;
				}

				.acf-field-global-settings-global-settings > .acf-input > .acf-fields.-top.-border,
				.acf-field-global-settings-global-settings > .acf-input .acf-fields .acf-fields.-top.-border{
					border:0 !important;
				}

				.acf-label {
					margin: 0 !important;
				}
			</style>
		<?php
	}

	protected function registerFields(): array
	{
		$post_types = array_diff(get_post_types(), [
            'acf-field',
            'acf-field-group',
            'acf-post-type',
            'acf-taxonomy',
            'acf-ui-options-page',
            'attachment',
            'custom_css',
            'customize_changeset',
            'frm_styles',
            'frm_form_actions',
            'nav_menu_item',
            'oembed_cache',
            'revision',
            'user_request',
            'wp_block',
            'wp_font_face',
            'wp_font_family',
            'wp_global_styles',
            'wp_navigation',
            'wp_template',
            'wp_template_part',
		]);

		$fallback = new FieldsBuilder('cpt_fallback');

		foreach ($post_types as $post_type) {
			$fallback->addTab($post_type, ['placement' => 'left'])
				->addGroup(str_replace(' ', '_', strtolower($post_type)), ['label' => ''])
				->addImage('image')
				->endGroup();
		}
		$no_results = new FieldsBuilder('cpt_no_result');

		foreach ($post_types as $post_type) {
			$no_results->addTab($post_type, ['placement' => 'left'])
				->addGroup(str_replace(' ', '_', strtolower($post_type)), ['label' => ''])
				->addImage('image')
				->addText('title')
				->addTextarea('message')
				->endGroup();
		}
		$_postTypeOptions = [];
		foreach ( $post_types as $post_type ){
			$_postTypeOptions[$post_type] =  str_replace( '-', ' ', ucwords( Inflect::pluralize( $post_type ) ) );
		}
		$block = new FieldsBuilder('global_settings');
		$block
			->setLocation('options_page', '==', 'global-settings');

		$block->addGroup('global_settings', ['label' => ''])
			->addTab('Contact Details')
			->addGroup('contact_details', ['label' => '','wrapper'=>['width'=>'50']])
				->addText('email')
				->addText('number')
			->endGroup()
			->addGroup('social', ['label' => '','wrapper'=>['width'=>'50']])
				->addText('facebook_page')
				->addText('discord_community')
				->addText('linkedin')
				->addText('twitter_page')
				->addText('x_page')
				->addText('github_account')
				->addText('youtube')
				->addText('instagram')
				->addText('whatsapp')
			->endGroup()
			->addTab('Fallback Images')
			->addGroup('fallback_image', ['label' => ''])
				->addFields($fallback)
			->endGroup()
			->addTab('No Results')
			->addGroup('no_results', ['label' => ''])
				->addFields($no_results)
			->endGroup()
			->addTab( 'Search Page' )
			->addGroup( 'search_page', [ 'label' => '' ] )
			->addCheckbox( 'searchable_post_types',[
				'label' => 'Include in Search',
				'choices'=> $_postTypeOptions
			])
			->endGroup()
			->addTab('API Keys')
			->addGroup('api_keys', ['label' => ''])
			->addTab('Google', ['placement' => 'left'])
			->addGroup('google', ['label' => ''])
			->addText('frontend_map')
			->addText('backend_map')
			->endGroup()
			->endGroup();

		return $block->build();
	}

	public function add_global_options_page()
	{
		acf_add_options_page(array(
			'page_title' => 'Global Settings',
			'menu_title' => 'Global Settings',
			'menu_slug' => 'global-settings',
			'capability' => 'edit_posts',
			'redirect' => false,
			'icon_url' => 'dashicons-admin-site-alt3',
			'position' => 2
		));
	}


	function add_custom_admin_bar_link() {
		global $wp_admin_bar;

		// Change 'Global Settings' to the text you want to display in the admin bar
		$wp_admin_bar->add_menu( array(
			'id'    => 'global_settings_link',
			'title' => 'Global Settings',
			'href'  => admin_url( 'admin.php?page=global-settings' ),
			'meta'  => array(
				'target' => '_self', // Change if you want the link to open in a new tab
			),
		) );
	}

	public static function get_option( $key ) {
		$options = self::get_options();

		return $options->{$key} ?? null;
	}

	public static function get_options() {
		if ( empty( self::$theme_options ) ) {
			// Retrieve the theme options from the database
			self::$theme_options = GlobalFunction::get_field( 'global_settings', 'options' );

			// Ensure $theme_options is an array to avoid issues
			if ( ! self::$theme_options instanceof \stdClass ) {
				self::$theme_options = new \stdClass;
			}
		}

		return self::$theme_options;
	}
	function populate_formidable_forms_choices( $field ) {
		ob_start();
		FrmFormsHelper::forms_dropdown( 'frm_add_form_id' );
		$forms = ob_get_contents();
		ob_end_clean();
		preg_match_all( '/<option\svalue="([^"]*)" >([^>]*)<\/option>/', $forms, $matches );
		$forms = array_combine( $matches[1], $matches[2] );


		// Check if forms exist
		if ( ! empty( $forms ) ) {
			// Initialize an empty array to store form titles and IDs
			$choices = array();

			// Loop through each form
			foreach ( $forms as $key => $form ) {
				$choices[ $key ] = $form;
			}

			// Set the choices for the select field
			$field['choices'] = $choices;
		} else {
			// If no forms are found, set default message
			$field['choices'] = array(
				'none' => 'No Formidable forms found'
			);
		}

		// Return the field
		return $field;
	}
}
