<?php

namespace NeurodiversityMan\CPT;

use NeurodiversityMan\Core\AcfFramework\FieldNameCollisionException;
use NeurodiversityMan\Helpers\RegisterCpt;
use stdClass;

class Book
{
    /**
     * Data.
     *
     * @var  stdClass|bool $data Holds the ACF Data.
     */

    public stdClass|bool $data;

    /**
     * @throws FieldNameCollisionException
     */
    function __construct()
    {
        $this->jbc_cpt();
    }

    public function jbc_cpt()
    {
        $cpt = new RegisterCpt();

        $cpt->registerCpt('book');

    }
}
