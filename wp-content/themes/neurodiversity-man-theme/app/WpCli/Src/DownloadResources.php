<?php

namespace NeurodiversityMan\WpCli\Src;

use ZipArchive;

class DownloadResources
{
    public static function getResource(string $url, string $zipFile, string $extractDir): void
    {
        $zipFile = get_stylesheet_directory() . '/' . $zipFile;
        $zipResource = fopen($zipFile, "w");

        // Get The Zip File From Server
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_FAILONERROR, true);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_AUTOREFERER, true);
        curl_setopt($ch, CURLOPT_BINARYTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_FILE, $zipResource);

        $page = curl_exec($ch);

        if (!$page) {
            echo "Error :- " . curl_error($ch);
        }

        curl_close($ch);

        /* Open the Zip file */
        $zip = new ZipArchive;
        $extractPath = get_stylesheet_directory() . '/' . $extractDir;

        if ($zip->open($zipFile) != "true") {
            echo "Error :- Unable to open the Zip File";
        }

        /* Extract Zip File */
        $zip->extractTo($extractPath . '-temp');
        $zip->close();

        $files = scandir($extractPath . '-temp', SCANDIR_SORT_DESCENDING);
        $newest_file = $files[0];
        rename($extractPath . '-temp/' . $newest_file, $extractPath);
        rmdir($extractPath . '-temp');
        unlink($zipFile);
    }
}
