<?php

namespace NeurodiversityMan\WpCli;

use NeurodiversityMan\WpCli\Src\DownloadResources;
use WP_CLI;
use function WP_CLI\Utils\get_flag_value;

class BlockCli
{
	/**
	 * @throws \Exception
	 */
	public function __construct()
    {
        WP_CLI::add_command('create-block', self::class);
    }

    public function __invoke($args, $assoc_args): void
    {

        $blockName = (!empty(get_flag_value($assoc_args, 'block-name'))) ? get_flag_value($assoc_args, 'block-name') : $args[0];
        if (!$blockName) {
            \WP_CLI::log('Please provide name for block. (e.g wp create-block "Example")');
        } else {
            $blockKeyName = strtolower(str_replace(' ', '-', $blockName));
            $dirName = ucwords(str_replace(' ', '', $blockName));
            $newPath = get_stylesheet_directory() . '/app/Blocks/' . $dirName . 'Block';

            if (file_exists($newPath) || is_dir($newPath)) {
                WP_CLI::log($dirName . 'Block exists at ' . $newPath);
            } else {

                DownloadResources::getResource(self::get_source($assoc_args),
                    'block-template.zip', 'BlockTemplate');
                $this->custom_copy(get_stylesheet_directory() . '/BlockTemplate/blockname-block', $newPath);
                rename($newPath . "/blockname-block.php", $newPath . "/" . $dirName . "Block.php");
                rename($newPath . "/src/sass/blockname-block.scss", $newPath . "/src/sass/$blockKeyName-block.scss");
                rename($newPath . "/src/scripts/blockname-block.js", $newPath . "/src/scripts/$blockKeyName-block.js");
                rename($newPath . "/src/scripts/editor.js", $newPath . "/src/scripts/editor.js");

                $classFile = file_get_contents($newPath . "/" . $dirName . "Block.php");
                $classFile = str_replace('CLASSBLOCKNAME', str_replace(' ', '', ucwords($blockName)), $classFile);
                $classFile = str_replace('BLOCKNAME', ucwords($blockName), $classFile);
                $classFile = str_replace('blockkeyname', $blockKeyName, $classFile);
                $classFile = str_replace('blockname', $dirName, $classFile);

                file_put_contents($newPath . "/" . $dirName . "Block.php", $classFile);

                $renderFile = file_get_contents($newPath . "/view/render.blade.php");
                $renderFile = str_replace('CLASSBLOCKNAME', str_replace(' ', '', ucwords($blockName)), $renderFile);
                $renderFile = str_replace('BLOCKNAME', ucwords($blockName), $renderFile);
                $renderFile = str_replace('blockname', $dirName, $renderFile);
                file_put_contents($newPath . "/view/render.blade.php", $renderFile);

                $sassFile = file_get_contents($newPath . "/src/sass/$blockKeyName-block.scss");
                $sassFile = str_replace('blockkeyname', $blockKeyName, $sassFile);
                file_put_contents($newPath . "/src/sass/$blockKeyName-block.scss", $sassFile);

                $scriptsFile = file_get_contents($newPath . "/src/scripts/$blockKeyName-block.js");
                $scriptsFile = str_replace('blockkeyname', $blockKeyName, $scriptsFile);
                file_put_contents($newPath . "/src/scripts/$blockKeyName-block.js", $scriptsFile);

                $editorFile = file_get_contents($newPath . "/src/scripts/editor.js");
                $editorFile = str_replace('blockkeyname', $blockKeyName, $editorFile);
                file_put_contents($newPath . "/src/scripts/editor.js", $editorFile);

                $this->rmdir_recursive(get_stylesheet_directory() . '/BlockTemplate');
                WP_CLI::log($newPath);
            }
        }
    }

    private static function get_source($assoc_args): string
    {
        $template = get_flag_value($assoc_args, 'block-template', $default = null);
        if ($template !== null) {
            $template = strtolower($template);
        }
        return match ($template) {
            'query' => 'https://bitbucket.org/jbcole/block-template/get/Query.zip',
            'skeleton' => 'https://bitbucket.org/jbcole/block-template/get/Skeleton.zip',
            'childtheme' => 'https://bitbucket.org/jbcole/block-template/get/ChildTheme.zip',
            'blade' => 'https://bitbucket.org/jbcole/block-template/get/Blade.zip',
            default => 'https://bitbucket.org/jbcole/block-template/get/NewBlade.zip',
        };
    }

    function custom_copy($src, $dst): void
    {

        // open the source directory
        $dir = opendir($src);

        // Make the destination directory if not exist
        @mkdir($dst);

        // Loop through the files in source directory
        while ($file = readdir($dir)) {

            if (($file != '.') && ($file != '..')) {
                if (is_dir($src . '/' . $file)) {

                    // Recursively calling custom copy function
                    // for subdirectory
                    $this->custom_copy($src . '/' . $file, $dst . '/' . $file);

                } else {
                    copy($src . '/' . $file, $dst . '/' . $file);
                }
            }
        }

        closedir($dir);
    }

    function rmdir_recursive($dir): void
    {
        foreach (scandir($dir) as $file) {
            if ('.' === $file || '..' === $file) continue;
            if (is_dir("$dir/$file")) $this->rmdir_recursive("$dir/$file");
            else unlink("$dir/$file");
        }
        rmdir($dir);
    }
}
