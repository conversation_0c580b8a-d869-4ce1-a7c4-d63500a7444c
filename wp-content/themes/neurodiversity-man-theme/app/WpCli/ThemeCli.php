<?php

namespace NeurodiversityMan\WpCli;

use WP_CLI;

/**
 * Theme configuration and setup commands
 */
class ThemeCli
{
    /**
     * Register the command with WP-CLI
     *
     * @throws \Exception
     */
    public function __construct()
    {
        if (class_exists('WP_CLI')) {
            WP_CLI::add_command('theme setup', [$this, 'setup']);
            WP_CLI::add_command('theme config', [$this, 'config']);
            WP_CLI::add_command('theme clear-cache', [$this, 'clearCache']);
        }
    }

    /**
     * Interactive theme setup
     *
     * ## EXAMPLES
     *
     *     wp theme setup
     *
     * @when after_wp_load
     */
    public function setup($args, $assoc_args): void
    {
        WP_CLI::log('🎨 WordPress Blade Theme Setup');
        WP_CLI::log('==============================');

        // Get theme details
        $theme_name = $this->prompt('Theme Name', 'WordPress Blade Theme');
        $theme_slug = $this->prompt('Theme Slug', $this->slugify($theme_name));
        $description = $this->prompt('Theme Description', 'A modern WordPress theme built with BladeOne and TailwindCSS');
        $author = $this->prompt('Author Name', 'Your Name');
        $author_uri = $this->prompt('Author Website', 'https://your-website.com');
        $version = $this->prompt('Initial Version', '1.0.0');
        $text_domain = $this->prompt('Text Domain', $theme_slug);

        // Update style.css
        $this->updateStyleCss([
            'theme_name' => $theme_name,
            'author' => $author,
            'author_uri' => $author_uri,
            'description' => $description,
            'version' => $version,
            'text_domain' => $text_domain,
        ]);

        // Update package.json
        $this->updatePackageJson([
            'name' => $theme_slug,
            'description' => $description,
            'version' => $version,
            'author' => $author,
        ]);

        // Create translation file
        $this->createTranslationFile($text_domain);

        // Create ACF options page
        $this->createOptionsPage($theme_name, $theme_slug, $text_domain);

        WP_CLI::success('Theme setup complete!');
        WP_CLI::log('');
        WP_CLI::log('Next steps:');
        WP_CLI::log('1. Run `npm install` to install dependencies');
        WP_CLI::log('2. Run `npm run dev` to build assets');
        WP_CLI::log('3. Activate the theme in WordPress');
        WP_CLI::log('4. Configure ACF fields as needed');
    }

    /**
     * Update theme configuration
     *
     * ## OPTIONS
     *
     * [--name=<name>]
     * : Theme name
     *
     * [--version=<version>]
     * : Theme version
     *
     * [--author=<author>]
     * : Theme author
     *
     * ## EXAMPLES
     *
     *     wp theme config --name="My Theme" --version="2.0.0"
     *
     * @when after_wp_load
     */
    public function config($args, $assoc_args): void
    {
        $updated = [];

        if (isset($assoc_args['name'])) {
            $this->updateStyleCss(['theme_name' => $assoc_args['name']]);
            $updated[] = 'Theme name';
        }

        if (isset($assoc_args['version'])) {
            $this->updateStyleCss(['version' => $assoc_args['version']]);
            $this->updatePackageJson(['version' => $assoc_args['version']]);
            $updated[] = 'Version';
        }

        if (isset($assoc_args['author'])) {
            $this->updateStyleCss(['author' => $assoc_args['author']]);
            $this->updatePackageJson(['author' => $assoc_args['author']]);
            $updated[] = 'Author';
        }

        if (!empty($updated)) {
            WP_CLI::success('Updated: ' . implode(', ', $updated));
        } else {
            WP_CLI::warning('No configuration options provided');
        }
    }

    /**
     * Clear all theme caches
     *
     * ## EXAMPLES
     *
     *     wp theme clear-cache
     *
     * @when after_wp_load
     */
    public function clearCache($args, $assoc_args): void
    {
        WP_CLI::log('Clearing theme caches...');

        // Clear Blade cache
        $blade_cache = WP_CONTENT_DIR . '/cache/.wp-bladeone-cache';
        if (is_dir($blade_cache)) {
            $this->deleteDirectory($blade_cache);
            mkdir($blade_cache, 0755, true);
            WP_CLI::success('Blade cache cleared');
        }

        // Clear WordPress transients
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_%'");
        WP_CLI::success('WordPress transients cleared');

        // Clear object cache if available
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
            WP_CLI::success('Object cache flushed');
        }

        WP_CLI::success('All caches cleared!');
    }

    /**
     * Prompt for user input
     */
    private function prompt($question, $default = ''): string
    {
        $default_text = $default ? " [$default]" : '';
        fwrite(STDOUT, "$question$default_text: ");
        $input = trim(fgets(STDIN));
        return $input ?: $default;
    }

    /**
     * Convert string to slug
     */
    private function slugify($text): string
    {
        return strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $text), '-'));
    }

    /**
     * Update style.css header
     */
    private function updateStyleCss($config): void
    {
        $style_path = get_stylesheet_directory() . '/style.css';
        $current = file_get_contents($style_path);

        // Parse current header
        preg_match('/\/\*([^*]|[\r\n]|(\*+([^*\/]|[\r\n])))*\*+\//', $current, $matches);
        $header = $matches[0] ?? '';

        // Update values
        $replacements = [
            'Theme Name' => $config['theme_name'] ?? null,
            'Author' => $config['author'] ?? null,
            'Author URI' => $config['author_uri'] ?? null,
            'Description' => $config['description'] ?? null,
            'Version' => $config['version'] ?? null,
            'Text Domain' => $config['text_domain'] ?? null,
        ];

        foreach ($replacements as $key => $value) {
            if ($value !== null) {
                $header = preg_replace(
                    '/' . preg_quote($key, '/') . ':\s*.*$/m',
                    "$key: $value",
                    $header
                );
            }
        }

        // Write back
        $new_content = $header . substr($current, strlen($matches[0]));
        file_put_contents($style_path, $new_content);
    }

    /**
     * Update package.json
     */
    private function updatePackageJson($config): void
    {
        $package_path = get_stylesheet_directory() . '/package.json';
        $package = json_decode(file_get_contents($package_path), true);

        foreach ($config as $key => $value) {
            $package[$key] = $value;
        }

        file_put_contents($package_path, json_encode($package, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
    }

    /**
     * Create translation template file
     */
    private function createTranslationFile($text_domain): void
    {
        $languages_dir = get_stylesheet_directory() . '/languages';
        if (!is_dir($languages_dir)) {
            mkdir($languages_dir, 0755, true);
        }

        $pot_file = "$languages_dir/$text_domain.pot";
        $pot_content = <<<POT
# Copyright (C) {year} {author}
# This file is distributed under the GPL v2 or later.
msgid ""
msgstr ""
"Project-Id-Version: {theme_name} {version}\\n"
"Report-Msgid-Bugs-To: {author_uri}\\n"
"POT-Creation-Date: {date}\\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\\n"
"Language-Team: LANGUAGE <<EMAIL>>\\n"
"X-Domain: {text_domain}\\n"
POT;

        $replacements = [
            '{year}' => date('Y'),
            '{author}' => get_option('theme_author', 'Your Name'),
            '{theme_name}' => get_option('theme_name', 'WordPress Blade Theme'),
            '{version}' => get_option('theme_version', '1.0.0'),
            '{author_uri}' => get_option('theme_author_uri', 'https://your-website.com'),
            '{date}' => date('c'),
            '{text_domain}' => $text_domain,
        ];

        $pot_content = str_replace(array_keys($replacements), array_values($replacements), $pot_content);
        file_put_contents($pot_file, $pot_content);
    }

    /**
     * Create ACF options page
     */
    private function createOptionsPage($theme_name, $theme_slug, $text_domain): void
    {
        $options_file = get_stylesheet_directory() . '/app/Custom/Functions/ThemeOptions.php';

        // Create directory if it doesn't exist
        $dir = dirname($options_file);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        // Skip if file already exists
        if (file_exists($options_file)) {
            return;
        }

        $namespace = $this->getNamespace();
        $options_content = <<<PHP
<?php

namespace {$namespace}\\Custom\\Functions;

use {$namespace}\\Core\\AcfFramework\\FieldsBuilder;

/**
 * Theme Options Page
 */
class ThemeOptions
{
    public function __construct()
    {
        add_action('acf/init', [\$this, 'createOptionsPage']);
        add_action('acf/init', [\$this, 'registerFields']);
    }

    public function createOptionsPage(): void
    {
        if (!function_exists('acf_add_options_page')) {
            return;
        }

        acf_add_options_page([
            'page_title' => __('{$theme_name} Settings', '{$text_domain}'),
            'menu_title' => __('Theme Settings', '{$text_domain}'),
            'menu_slug'  => '{$theme_slug}-settings',
            'capability' => 'edit_theme_options',
            'icon_url'   => 'dashicons-admin-appearance',
            'position'   => 60,
        ]);
    }

    public function registerFields(): void
    {
        if (!function_exists('acf_add_local_field_group')) {
            return;
        }

        \$settings = new FieldsBuilder('theme_settings');

        \$settings
            ->addTab('general', ['label' => __('General', '{$text_domain}')])
                ->addImage('site_logo', [
                    'label' => __('Site Logo', '{$text_domain}'),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                ])
                ->addText('copyright_text', [
                    'label' => __('Copyright Text', '{$text_domain}'),
                    'default_value' => '© ' . date('Y') . ' {$theme_name}. All rights reserved.',
                ])
            ->setLocation('options_page', '==', '{$theme_slug}-settings');

        acf_add_local_field_group(\$settings->build());
    }
}

// Initialize
new ThemeOptions();
PHP;

        file_put_contents($options_file, $options_content);
    }

    /**
     * Get the theme namespace from composer.json
     */
    private function getNamespace(): string
    {
        $composer_path = get_stylesheet_directory() . '/composer.json';
        if (file_exists($composer_path)) {
            $composer = json_decode(file_get_contents($composer_path), true);
            if (isset($composer['autoload']['psr-4'])) {
                $namespace = array_keys($composer['autoload']['psr-4'])[0];
                return rtrim($namespace, '\\');
            }
        }
        return 'NeurodiversityMan';
    }

    /**
     * Recursively delete a directory
     */
    private function deleteDirectory($dir): bool
    {
        if (!is_dir($dir)) {
            return false;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            is_dir($path) ? $this->deleteDirectory($path) : unlink($path);
        }

        return rmdir($dir);
    }
}

// Initialize the command
new ThemeCli();
