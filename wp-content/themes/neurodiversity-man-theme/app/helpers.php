<?php

namespace JBC;


use Exception;
use NeurodiversityMan\Core\BladeOne\BladeOne;

/**
 * @param string $file
 * @param array $data
 *
 * @return string
 * @throws Exception
 */
function template(string $file, array $data = []): string
{
    $blade = new BladeOne(WP_BLADEONE_VIEWS, WP_BLADEONE_CACHE, WP_BLADEONE_MODE);
    return $blade->run(str_replace('.blade.php', '', basename($file)), $data);

}

/**
 * @param string|string[] $templates Possible template files
 *
 * @return array
 */
function filter_templates(array|string $templates): array
{

    $paths = apply_filters('filter_templates/paths', [
        'resources/views'
    ]);
    $paths_pattern = "#^(" . implode('|', $paths) . ")/#";
    return collect($templates)
        ->map(function ($template) use ($paths_pattern) {
            /** Remove .blade.php/.blade/.php from template names */
            $template = preg_replace('#\.(blade\.?)?(php)?$#', '', ltrim($template));
            /** Remove partial $paths from the beginning of template names */
            if (strpos($template, '/')) {
                $template = preg_replace($paths_pattern, '', $template);
            }

            return $template;
        })
        ->flatMap(function ($template) use ($paths) {
            return collect($paths)
                ->flatMap(function ($path) use ($template) {
                        return [
                            "{$path}/{$template}",
                            "{$path}/{$template}.blade.php",
                        ];

                })
                ->concat([
                    "{$template}",
                    "{$template}.blade.php",
                ]);
        })
        ->filter()
        ->unique()
        ->all();
}

/**
 * @param string|string[] $templates Relative path to possible template files
 *
 * @return string Location of the template
 */
function locate_template(array|string $templates): string
{
    return \locate_template(filter_templates($templates));
}

/**
 * Determine whether to show the sidebar
 * @return bool
 */
function display_sidebar(): bool
{
    static $display;
    isset($display) || $display = apply_filters('display_sidebar', false);
    return $display;
}
