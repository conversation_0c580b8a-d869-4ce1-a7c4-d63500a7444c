/**
 * Custom Customizer Controls Styles
 */

/* Theme Color Control Styles */
.theme-color-control-wrapper {
    margin-top: 10px;
}

.theme-color-palette h4 {
    margin: 10px 0 5px 0;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    color: #555;
    letter-spacing: 0.5px;
}

.theme-color-swatches {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
    gap: 8px;
    margin-bottom: 15px;
}

.theme-color-swatch {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    border: 2px solid #ddd;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.theme-color-swatch:hover {
    transform: scale(1.1);
    border-color: #0073aa !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    z-index: 10;
}

.theme-color-swatch.selected {
    border-color: #0073aa !important;
    box-shadow: 0 0 0 2px #0073aa;
    transform: scale(1.05);
    z-index: 5;
}

.theme-color-swatch::after {
    content: attr(title);
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.theme-color-swatch:hover::after {
    opacity: 1;
}

.custom-color-trigger {
    background: linear-gradient(45deg, #ccc 25%, transparent 25%), 
                linear-gradient(-45deg, #ccc 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #ccc 75%), 
                linear-gradient(-45deg, transparent 75%, #ccc 75%);
    background-size: 8px 8px;
    background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #666;
    font-weight: bold;
}

.custom-color-trigger:hover {
    background-color: #f0f0f0;
    color: #0073aa;
}

.theme-color-hex {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    transition: border-color 0.2s ease;
}

.theme-color-hex:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.theme-color-input {
    width: 100%;
    height: 40px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 10px;
    cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 782px) {
    .theme-color-swatches {
        grid-template-columns: repeat(auto-fit, minmax(35px, 1fr));
        gap: 6px;
    }
    
    .theme-color-swatch {
        width: 35px;
        height: 35px;
    }
    
    .custom-color-trigger {
        font-size: 16px;
    }
}
