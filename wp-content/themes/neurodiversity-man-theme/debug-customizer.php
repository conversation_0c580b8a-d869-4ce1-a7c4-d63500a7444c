<?php
/**
 * Temporary debugging file for customizer issues
 * Add this to your theme temporarily to debug the footer logo issue
 * 
 * Usage: Add this line to your functions.php or any template file:
 * include get_template_directory() . '/debug-customizer.php';
 */

// Only run for administrators
if (!current_user_can('manage_options')) {
    return;
}

// Add admin notice with debug info
add_action('admin_notices', function() {
    $logo_id = get_theme_mod('custom_footer_logo');
    $all_theme_mods = get_theme_mods();
    
    echo '<div class="notice notice-info">';
    echo '<h3>Customizer Debug Information</h3>';
    
    echo '<h4>Footer Logo Specific:</h4>';
    echo '<ul>';
    echo '<li><strong>custom_footer_logo value:</strong> ' . var_export($logo_id, true) . '</li>';
    echo '<li><strong>Type:</strong> ' . gettype($logo_id) . '</li>';
    echo '<li><strong>Empty check:</strong> ' . (empty($logo_id) ? 'TRUE' : 'FALSE') . '</li>';
    
    if ($logo_id) {
        echo '<li><strong>Is valid attachment:</strong> ' . (wp_attachment_is_image($logo_id) ? 'TRUE' : 'FALSE') . '</li>';
        echo '<li><strong>Attachment URL:</strong> ' . wp_get_attachment_url($logo_id) . '</li>';
        echo '<li><strong>Attachment exists:</strong> ' . (get_post($logo_id) ? 'TRUE' : 'FALSE') . '</li>';
    }
    echo '</ul>';
    
    echo '<h4>All Theme Mods:</h4>';
    echo '<pre style="background: #f9f9f9; padding: 10px; max-height: 300px; overflow-y: auto;">';
    print_r($all_theme_mods);
    echo '</pre>';
    
    echo '<h4>WordPress Database Options:</h4>';
    $theme_name = get_option('stylesheet');
    $theme_mods_option = get_option("theme_mods_$theme_name");
    echo '<pre style="background: #f9f9f9; padding: 10px; max-height: 200px; overflow-y: auto;">';
    print_r($theme_mods_option);
    echo '</pre>';
    
    echo '</div>';
});

// Add a simple test function you can call
function test_footer_logo_upload() {
    if (!current_user_can('manage_options')) {
        return 'Access denied';
    }
    
    $logo_id = get_theme_mod('custom_footer_logo');
    
    if (empty($logo_id)) {
        return 'No footer logo set. Please go to Appearance > Customize > Site Identity and upload a footer logo.';
    }
    
    if (!wp_attachment_is_image($logo_id)) {
        return 'Footer logo ID exists but is not a valid image attachment.';
    }
    
    $url = wp_get_attachment_url($logo_id);
    if (!$url) {
        return 'Footer logo attachment exists but has no URL.';
    }
    
    return 'Footer logo is working! ID: ' . $logo_id . ', URL: ' . $url;
}

// Add a shortcode for easy testing
add_shortcode('test_footer_logo', 'test_footer_logo_upload');

// Add admin bar link for quick testing
add_action('admin_bar_menu', function($wp_admin_bar) {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    $wp_admin_bar->add_node([
        'id' => 'test_footer_logo',
        'title' => 'Test Footer Logo',
        'href' => admin_url('customize.php?autofocus[control]=custom_footer_logo'),
    ]);
}, 999);
