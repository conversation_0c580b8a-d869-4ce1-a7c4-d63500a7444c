// theme.config.js
const { transform } = require('theme-json-generator'); // Plugin helper for transforming data.
const { theme } = require('./tailwind.config'); // Import Tailwind config.

module.exports = {
	theme: {
		extend: {
			backgroundImage: {
				'custom-radial':
					'radial-gradient(75% 75% at 50% 50%, #71C4FF 1%, #71C4FF 1%, #002e40 99%)',
			},
		},
	},
	settings: {
		color: {
			palette: transform('palette', theme.extend.colors),
		},
		layout: {
			contentSize: theme.extend.layout.contentSize || '',
			wideSize: theme.extend.layout.wideSize || '',
		},
		spacing: {
			margin: theme.extend.spacing.margin,
			padding: theme.extend.spacing.padding,
		},
		background: {
			backgroundImage: theme.extend.background.backgroundImage,
			backgroundSize: theme.extend.background.backgroundSize,
		},
	},
	styles: {
		elements: {
			link: {
				typography: {
					textDecoration: 'none',
				},
			},
		},
	},

	safelist: ['top-[0.75rem]', 'right-[10rem]'],
};
