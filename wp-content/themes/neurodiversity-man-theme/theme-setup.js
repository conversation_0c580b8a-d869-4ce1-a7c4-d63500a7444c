#!/usr/bin/env node

/**
 * WordPress Theme Setup Script
 *
 * This script configures the theme by updating various files with user-provided information.
 * It updates style.css, package.json, composer.json, and various PHP files with the theme details.
 */

const fs = require('fs').promises;
const path = require('path');
const prompts = require('prompts');
const { execSync } = require('child_process');

// Color codes for terminal output
const colors = {
	reset: '\x1b[0m',
	bright: '\x1b[1m',
	green: '\x1b[32m',
	blue: '\x1b[34m',
	yellow: '\x1b[33m',
	red: '\x1b[31m',
	cyan: '\x1b[36m',
};

// Helper function to display colored output
const log = {
	info: (msg) => console.log(`${colors.blue}ℹ${colors.reset}  ${msg}`),
	success: (msg) => console.log(`${colors.green}✓${colors.reset}  ${msg}`),
	warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset}  ${msg}`),
	error: (msg) => console.log(`${colors.red}✖${colors.reset}  ${msg}`),
	title: (msg) =>
		console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}\n`),
};

// Default values
const defaults = {
	themeName: 'WordPress Blade Theme',
	themeSlug: 'wordpress-blade-theme',
	description: 'A modern WordPress theme built with BladeOne and TailwindCSS',
	author: 'Your Name',
	authorUri: 'https://your-website.com',
	version: '1.0.0',
	textDomain: 'wordpress-blade-theme',
	namespace: 'WPBladeTheme',
	functionPrefix: 'wp_blade_theme',
};

// Questions for the setup
const questions = [
	{
		type: 'text',
		name: 'themeName',
		message: 'Theme Name:',
		initial: defaults.themeName,
	},
	{
		type: 'text',
		name: 'themeSlug',
		message: 'Theme Slug (lowercase, no spaces):',
		initial: (prev) => prev.toLowerCase().replace(/\s+/g, '-'),
		validate: (value) =>
			/^[a-z0-9-]+$/.test(value) ||
			'Only lowercase letters, numbers, and hyphens allowed',
	},
	{
		type: 'text',
		name: 'description',
		message: 'Theme Description:',
		initial: defaults.description,
	},
	{
		type: 'text',
		name: 'author',
		message: 'Author Name:',
		initial: defaults.author,
	},
	{
		type: 'text',
		name: 'authorUri',
		message: 'Author Website:',
		initial: defaults.authorUri,
		validate: (value) => {
			try {
				new URL(value);
				return true;
			} catch {
				return 'Please enter a valid URL';
			}
		},
	},
	{
		type: 'text',
		name: 'version',
		message: 'Initial Version:',
		initial: defaults.version,
		validate: (value) =>
			/^\d+\.\d+\.\d+$/.test(value) ||
			'Please use semantic versioning (e.g., 1.0.0)',
	},
	{
		type: 'text',
		name: 'textDomain',
		message: 'Text Domain (for translations):',
		initial: (prev, values) => values.themeSlug,
		validate: (value) =>
			/^[a-z0-9-]+$/.test(value) ||
			'Only lowercase letters, numbers, and hyphens allowed',
	},
	{
		type: 'text',
		name: 'namespace',
		message: 'PHP Namespace (PascalCase):',
		initial: (prev, values) =>
			values.themeName.replace(/\s+/g, '').replace(/[^a-zA-Z0-9]/g, ''),
		validate: (value) =>
			/^[A-Z][a-zA-Z0-9]*$/.test(value) ||
			'Must start with uppercase letter, no spaces or special characters',
	},
	{
		type: 'text',
		name: 'functionPrefix',
		message: 'Function Prefix (lowercase with underscores):',
		initial: (prev, values) => values.themeSlug.replace(/-/g, '_'),
		validate: (value) =>
			/^[a-z][a-z0-9_]*$/.test(value) ||
			'Must start with lowercase letter, only letters, numbers, and underscores',
	},
];

// File update configurations
const fileUpdates = {
	'style.css': async (config) => {
		const content = `/*
Theme Name: ${config.themeName}
Author: ${config.author}
Author URI: ${config.authorUri}
Description: ${config.description}
Requires at least: 6.0
Tested up to: 6.7
Requires PHP: 8.0
Version: ${config.version}
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: ${config.textDomain}
*/

/* Theme styles are compiled from resources/sass/ - do not edit this file directly */
`;
		return content;
	},

	'package.json': async (config, currentContent) => {
		const packageJson = JSON.parse(currentContent);
		packageJson.name = config.themeSlug;
		packageJson.description = config.description;
		packageJson.version = config.version;
		packageJson.author = config.author;
		return JSON.stringify(packageJson, null, '\t');
	},

	'composer.json': async (config, currentContent) => {
		const composerJson = JSON.parse(currentContent);
		composerJson.name = `${config.author.toLowerCase().replace(/\s+/g, '-')}/${config.themeSlug}`;
		composerJson.description = config.description;

		// Update namespace in autoload
		if (composerJson.autoload && composerJson.autoload['psr-4']) {
			const oldNamespace = Object.keys(composerJson.autoload['psr-4'])[0];
			composerJson.autoload['psr-4'] = {
				[`${config.namespace}\\`]:
					composerJson.autoload['psr-4'][oldNamespace],
			};
		}

		// Update namespace in autoload-dev
		if (
			composerJson['autoload-dev'] &&
			composerJson['autoload-dev']['psr-4']
		) {
			const oldNamespace = Object.keys(
				composerJson['autoload-dev']['psr-4']
			)[0];
			composerJson['autoload-dev']['psr-4'] = {
				[`${config.namespace}\\Tests\\`]:
					composerJson['autoload-dev']['psr-4'][oldNamespace],
			};
		}

		return JSON.stringify(composerJson, null, '\t');
	},
};

// Replace text in file
async function replaceInFile(filePath, replacements) {
	try {
		let content = await fs.readFile(filePath, 'utf8');

		for (const [search, replace] of Object.entries(replacements)) {
			// Use regex with global flag to replace all occurrences
			const regex = new RegExp(
				search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
				'g'
			);
			content = content.replace(regex, replace);
		}

		await fs.writeFile(filePath, content);
		return true;
	} catch (error) {
		return false;
	}
}

// Update PHP files with new namespace and text domain
async function updatePhpFiles(config) {
	const replacements = {
		WPBladeBoilerplate: config.namespace,
		WordPressBladeTheme: config.namespace, // Add this line to handle existing namespace
		'wordpress-blade-theme': config.textDomain,
		wordpress_blade_theme: config.functionPrefix,
		'jbc-blade': config.textDomain, // Legacy text domain
	};

	const phpFiles = [
		'functions.php',
		'index.php',
		'app/setup.php',
		'app/filters.php',
		'app/helpers.php',
	];

	// Find all PHP files in app directory
	const findPhpFiles = async (dir) => {
		const files = [];
		const items = await fs.readdir(dir, { withFileTypes: true });

		for (const item of items) {
			const fullPath = path.join(dir, item.name);
			if (item.isDirectory()) {
				files.push(...(await findPhpFiles(fullPath)));
			} else if (item.name.endsWith('.php')) {
				files.push(fullPath);
			}
		}

		return files;
	};

	const appPhpFiles = await findPhpFiles('app');
	const allPhpFiles = [
		...phpFiles,
		...appPhpFiles.map((f) => path.relative('.', f)),
	];

	let updatedCount = 0;
	for (const file of allPhpFiles) {
		if (await replaceInFile(file, replacements)) {
			updatedCount++;
		}
	}

	return updatedCount;
}

// Update Blade template files
async function updateBladeFiles(config) {
	const replacements = {
		'wordpress-blade-theme': config.textDomain,
		WordPressBladeTheme: config.namespace, // Add namespace replacement
	};

	const findBladeFiles = async (dir) => {
		const files = [];
		const items = await fs.readdir(dir, { withFileTypes: true });

		for (const item of items) {
			const fullPath = path.join(dir, item.name);
			if (item.isDirectory()) {
				files.push(...(await findBladeFiles(fullPath)));
			} else if (item.name.endsWith('.blade.php')) {
				files.push(fullPath);
			}
		}

		return files;
	};

	const bladeFiles = await findBladeFiles('resources/views');

	let updatedCount = 0;
	for (const file of bladeFiles) {
		if (await replaceInFile(file, replacements)) {
			updatedCount++;
		}
	}

	return updatedCount;
}

// Main setup function
async function setup() {
	log.title('🎨 WordPress Blade Theme Setup');
	log.info('This will configure your theme with custom details.\n');

	// Get user input
	const config = await prompts(questions);

	// Check if user cancelled
	if (Object.keys(config).length === 0) {
		log.warning('Setup cancelled.');
		process.exit(0);
	}

	log.title('📝 Updating theme files...');

	// Update main configuration files
	for (const [file, updater] of Object.entries(fileUpdates)) {
		try {
			let currentContent = '';

			// Read current content if needed
			if (file.endsWith('.json')) {
				currentContent = await fs.readFile(file, 'utf8');
			}

			const newContent = await updater(config, currentContent);
			await fs.writeFile(file, newContent);

			log.success(`Updated ${file}`);
		} catch (error) {
			log.error(`Failed to update ${file}: ${error.message}`);
		}
	}

	// Update PHP files
	log.info('Updating PHP files...');
	const phpCount = await updatePhpFiles(config);
	log.success(`Updated ${phpCount} PHP files`);

	// Update Blade files
	log.info('Updating Blade template files...');
	const bladeCount = await updateBladeFiles(config);
	log.success(`Updated ${bladeCount} Blade template files`);

	// Generate translation file
	log.info('Generating translation template...');
	try {
		await fs.mkdir('languages', { recursive: true });
		const potContent = `# Copyright (C) ${new Date().getFullYear()} ${config.author}
# This file is distributed under the GPL v2 or later.
msgid ""
msgstr ""
"Project-Id-Version: ${config.themeName} ${config.version}\\n"
"Report-Msgid-Bugs-To: ${config.authorUri}\\n"
"Last-Translator: ${config.author}\\n"
"Language-Team: ${config.author}\\n"
"MIME-Version: 1.0\\n"
"Content-Type: text/plain; charset=UTF-8\\n"
"Content-Transfer-Encoding: 8bit\\n"
"POT-Creation-Date: ${new Date().toISOString()}\\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\\n"
"X-Generator: WP-CLI\\n"
"X-Domain: ${config.textDomain}\\n"
`;
		await fs.writeFile(`languages/${config.textDomain}.pot`, potContent);
		log.success('Created translation template');
	} catch (error) {
		log.error(`Failed to create translation template: ${error.message}`);
	}

	// Update composer autoload
	log.info('Updating Composer autoload...');
	try {
		execSync('composer dump-autoload --optimize', { stdio: 'inherit' });
		log.success('Updated Composer autoload with optimization');
	} catch (error) {
		log.warning(`Could not update Composer autoload: ${error.message}`);
		log.warning('Please run "composer dump-autoload --optimize" manually.');
	}

	// Create .env file if it doesn't exist
	try {
		await fs.access('.env');
	} catch {
		log.info('Creating .env file...');
		const envContent = `# WordPress Blade Theme Environment Variables

# Environment
WP_ENV=development

# Theme Settings
THEME_NAME="${config.themeName}"
THEME_VERSION=${config.version}

# Development
BROWSER_SYNC_PROXY=http://localhost:8080
BROWSER_SYNC_PORT=3000

# Build Settings
BUILD_SOURCEMAPS=true
BUILD_NOTIFICATIONS=true
`;
		await fs.writeFile('.env', envContent);
		log.success('Created .env file');
	}

	// Setup ACF options page
	log.info('Creating theme options setup...');
	const optionsContent = `<?php

namespace ${config.namespace}\\Custom\\Functions;

use ${config.namespace}\\Core\\AcfFramework\\FieldsBuilder;

/**
 * Theme Options Page
 */
class ThemeOptions
{
    public function __construct()
    {
        add_action('acf/init', [$this, 'createOptionsPage']);
        add_action('acf/init', [$this, 'registerFields']);
    }

    public function createOptionsPage(): void
    {
        if (!function_exists('acf_add_options_page')) {
            return;
        }

        acf_add_options_page([
            'page_title' => __('${config.themeName} Settings', '${config.textDomain}'),
            'menu_title' => __('Theme Settings', '${config.textDomain}'),
            'menu_slug'  => '${config.themeSlug}-settings',
            'capability' => 'edit_theme_options',
            'icon_url'   => 'dashicons-admin-appearance',
            'position'   => 60,
        ]);
    }

    public function registerFields(): void
    {
        if (!function_exists('acf_add_local_field_group')) {
            return;
        }

        $settings = new FieldsBuilder('theme_settings');

        $settings
            ->addTab('general', ['label' => __('General', '${config.textDomain}')])
                ->addImage('site_logo', [
                    'label' => __('Site Logo', '${config.textDomain}'),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                ])
                ->addText('copyright_text', [
                    'label' => __('Copyright Text', '${config.textDomain}'),
                    'default_value' => '© ' . date('Y') . ' ${config.themeName}. All rights reserved.',
                ])
            ->addTab('social', ['label' => __('Social Media', '${config.textDomain}')])
                ->addRepeater('social_links', [
                    'label' => __('Social Media Links', '${config.textDomain}'),
                    'button_label' => __('Add Social Link', '${config.textDomain}'),
                ])
                    ->addSelect('platform', [
                        'label' => __('Platform', '${config.textDomain}'),
                        'choices' => [
                            'facebook' => 'Facebook',
                            'twitter' => 'Twitter',
                            'instagram' => 'Instagram',
                            'linkedin' => 'LinkedIn',
                            'youtube' => 'YouTube',
                            'github' => 'GitHub',
                        ],
                    ])
                    ->addUrl('url', [
                        'label' => __('URL', '${config.textDomain}'),
                    ])
                ->endRepeater()
            ->setLocation('options_page', '==', '${config.themeSlug}-settings');

        acf_add_local_field_group($settings->build());
    }
}

// Initialize
new ThemeOptions();
`;

	try {
		await fs.mkdir('app/Custom/Functions', { recursive: true });
		await fs.writeFile(
			'app/Custom/Functions/ThemeOptions.php',
			optionsContent
		);
		log.success('Created theme options setup');
	} catch (error) {
		log.error(`Failed to create theme options: ${error.message}`);
	}

	// Final summary
	log.title('✨ Theme setup complete!');
	console.log('\nTheme Details:');
	console.log(
		`  Name:        ${colors.cyan}${config.themeName}${colors.reset}`
	);
	console.log(
		`  Version:     ${colors.cyan}${config.version}${colors.reset}`
	);
	console.log(
		`  Text Domain: ${colors.cyan}${config.textDomain}${colors.reset}`
	);
	console.log(
		`  Namespace:   ${colors.cyan}${config.namespace}${colors.reset}`
	);

	console.log('\nNext steps:');
	console.log(
		`  1. Run ${colors.yellow}npm run dev${colors.reset} to build assets`
	);
	console.log(`  2. Activate the theme in WordPress`);
	console.log(`  3. Configure ACF fields as needed`);
	console.log(`  4. Start developing! 🚀`);
}

// Error handler
process.on('unhandledRejection', (error) => {
	log.error(`Setup failed: ${error.message}`);
	process.exit(1);
});

// Run setup
setup().catch((error) => {
	log.error(`Setup failed: ${error.message}`);
	process.exit(1);
});
