{"name": "neurodiversity-man", "version": "1.0.0", "description": "A modern WordPress theme built with BladeOne and TailwindCSS", "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=development webpack --mode development", "watch": "cross-env NODE_ENV=development webpack --mode development --watch", "build": "cross-env NODE_ENV=production webpack --mode production", "lint": "run-p lint:*", "lint:eslint": "eslint resources/", "lint:prettier": "prettier --check .", "lint:php": "composer run-script phpcs", "lint:fix": "run-p lint-fix:*", "lint:fix:eslint": "eslint resources/ --fix", "lint:fix:prettier": "prettier --write .", "lint:fix:php": "composer run-script phpcbf", "theme:setup": "node theme-setup.js", "theme:create-block": "wp create-block", "prepare": "husky install"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "dependencies": {"@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "alpinejs": "^3.14.3", "cross-env": "^7.0.3", "jquery": "^3.7.1", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@wordpress/browserslist-config": "^6.13.0", "@wordpress/eslint-plugin": "^22.2.0", "@wordpress/prettier-config": "^4.13.0", "autoprefixer": "^10.4.20", "babel-loader": "^9.2.1", "browser-sync": "^3.0.3", "browser-sync-v3-webpack-plugin": "^0.1.0", "clean-webpack-plugin": "^4.0.0", "concurrently": "^9.1.2", "copy-webpack-plugin": "^12.0.2", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.0", "dotenv-webpack": "^8.1.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-tailwindcss": "^3.17.5", "glob": "^10.4.5", "husky": "^9.1.7", "image-minimizer-webpack-plugin": "^4.1.0", "imagemin": "^9.0.0", "imagemin-gifsicle": "^7.0.0", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^10.0.0", "imagemin-svgo": "^11.0.1", "lint-staged": "^15.3.0", "mini-css-extract-plugin": "^2.9.2", "npm-run-all": "^4.1.5", "postcss": "^8.4.49", "postcss-loader": "^8.1.1", "postcss-nesting": "^13.0.1", "postcss-preset-env": "^10.1.2", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "prompts": "^2.4.2", "resolve-url-loader": "^5.0.0", "sass": "^1.83.2", "sass-loader": "^16.0.4", "sharp": "^0.33.5", "style-loader": "^4.0.0", "stylelint": "^16.11.0", "stylelint-config-standard-scss": "^13.1.0", "terser-webpack-plugin": "^5.3.11", "theme-json-generator": "^0.3.0", "webpack": "^5.97.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.2.0", "webpack-merge": "^6.0.1", "webpackbar": "^6.0.1"}, "keywords": ["WordPress", "Themes", "BladeOne", "TailwindCSS", "FSE", "ACF", "Blocks", "Boilerplate"], "author": "<PERSON>", "license": "MIT", "private": true, "browserslist": "extends @wordpress/browserslist-config", "prettier": "@wordpress/prettier-config", "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write"], "*.{css,scss}": ["prettier --write"], "*.{json,md,yml}": ["prettier --write"], "*.php": ["prettier --write"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}