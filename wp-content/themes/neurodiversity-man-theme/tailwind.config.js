/** @type {import('tailwindcss').Config} */
const defaultTheme = require('tailwindcss/defaultTheme');

// Determine if we're building for the editor
const includePreflight = process.env._TW_TARGET === 'editor' ? false : true;

module.exports = {
	content: [
		'./app/**/*.php',
		'./resources/**/*.{php,blade.php,js,jsx,ts,tsx,vue}',
		'./templates/**/*.{html,php}',
		'./parts/**/*.{html,php}',
		'./patterns/**/*.{html,php}',
		'./blocks/**/*.{php,blade.php,js,jsx}',
		'!./resources/**/_*.{php,blade.php}', // Exclude partial files
	],
	safelist: [
		// Add any dynamic classes that might not be detected
		'prose',
		'prose-lg',
		'prose-xl',
		'prose-2xl',
	],
	theme: {
		extend: {
			colors: {
				primary: '#043873 ',
				secondary: '#4F9CF9',
				accent: '#FFE492',
				'body-text-dark': '#212529',
				'body-text-light': '#ffffff',
			},
			layout: {
				contentSize: '60rem',
				wideSize: '80rem',
			},

			background: {
				backgroundImage: true,
				backgroundSize: true,
			},
			fontFamily: {
				asap: [
					'ASAP',
					'ui-sans-serif',
					'system-ui',
					...defaultTheme.fontFamily.sans,
				],
				sans: ['Inter var', ...defaultTheme.fontFamily.sans],
				serif: ['Georgia', ...defaultTheme.fontFamily.serif],
				mono: ['JetBrains Mono', ...defaultTheme.fontFamily.mono],
			},
			fontSize: {
				xs: ['0.75rem', { lineHeight: '1rem' }],
				sm: ['0.875rem', { lineHeight: '1.25rem' }],
				base: ['1rem', { lineHeight: '1.5rem' }],
				lg: ['1.125rem', { lineHeight: '1.75rem' }],
				xl: ['1.25rem', { lineHeight: '1.75rem' }],
				'2xl': ['1.5rem', { lineHeight: '2rem' }],
				'3xl': ['1.875rem', { lineHeight: '2.25rem' }],
				'4xl': ['2.25rem', { lineHeight: '2.5rem' }],
				'5xl': ['3rem', { lineHeight: '1' }],
				'6xl': ['3.75rem', { lineHeight: '1' }],
				'7xl': ['4.5rem', { lineHeight: '1' }],
				'8xl': ['6rem', { lineHeight: '1' }],
				'9xl': ['8rem', { lineHeight: '1' }],
			},
			spacing: {
				128: '32rem',
				144: '36rem',
			},
			borderRadius: {
				'4xl': '2rem',
			},
			container: {
				center: true,
				padding: {
					DEFAULT: '1rem',
					sm: '2rem',
					lg: '4rem',
					xl: '5rem',
					'2xl': '6rem',
				},
			},
			screens: {
				xs: '475px',
				...defaultTheme.screens,
			},
			animation: {
				'fade-in': 'fade-in 0.5s ease-in-out',
				'fade-out': 'fade-out 0.5s ease-in-out',
				'slide-in-right': 'slide-in-right 0.5s ease-out',
				'slide-in-left': 'slide-in-left 0.5s ease-out',
				'slide-in-top': 'slide-in-top 0.5s ease-out',
				'slide-in-bottom': 'slide-in-bottom 0.5s ease-out',
			},
			keyframes: {
				'fade-in': {
					'0%': { opacity: '0' },
					'100%': { opacity: '1' },
				},
				'fade-out': {
					'0%': { opacity: '1' },
					'100%': { opacity: '0' },
				},
				'slide-in-right': {
					'0%': { transform: 'translateX(100%)' },
					'100%': { transform: 'translateX(0)' },
				},
				'slide-in-left': {
					'0%': { transform: 'translateX(-100%)' },
					'100%': { transform: 'translateX(0)' },
				},
				'slide-in-top': {
					'0%': { transform: 'translateY(-100%)' },
					'100%': { transform: 'translateY(0)' },
				},
				'slide-in-bottom': {
					'0%': { transform: 'translateY(100%)' },
					'100%': { transform: 'translateY(0)' },
				},
			},
			typography: (theme) => ({
				DEFAULT: {
					css: {
						color: theme('colors.gray.900'),
						a: {
							color: theme('colors.primary.600'),
							'&:hover': {
								color: theme('colors.primary.700'),
							},
						},
						'h1, h2, h3, h4, h5, h6': {
							color: theme('colors.gray.900'),
						},
						code: {
							color: theme('colors.pink.600'),
							backgroundColor: theme('colors.gray.100'),
							paddingLeft: '4px',
							paddingRight: '4px',
							paddingTop: '2px',
							paddingBottom: '2px',
							borderRadius: '0.25rem',
						},
						'code::before': {
							content: '""',
						},
						'code::after': {
							content: '""',
						},
					},
				},
				dark: {
					css: {
						color: theme('colors.gray.100'),
						a: {
							color: theme('colors.primary.400'),
							'&:hover': {
								color: theme('colors.primary.300'),
							},
						},
						'h1, h2, h3, h4, h5, h6': {
							color: theme('colors.gray.100'),
						},
						code: {
							color: theme('colors.pink.400'),
							backgroundColor: theme('colors.gray.800'),
						},
					},
				},
			}),
		},
	},
	corePlugins: {
		// Disable Preflight base styles in build targeting the editor
		preflight: includePreflight,
	},
	plugins: [
		require('@tailwindcss/forms')({
			strategy: 'class', // Use class strategy for better control
		}),
		require('@tailwindcss/typography'),
		require('@tailwindcss/container-queries'),
		require('tailwindcss/plugin')(function ({
			addBase,
			addComponents,
			addUtilities,
			theme,
		}) {
			// Add custom base styles
			addBase({
				h1: {
					fontSize: theme('fontSize.4xl'),
					fontWeight: theme('fontWeight.bold'),
				},
				h2: {
					fontSize: theme('fontSize.3xl'),
					fontWeight: theme('fontWeight.bold'),
				},
				h3: {
					fontSize: theme('fontSize.2xl'),
					fontWeight: theme('fontWeight.semibold'),
				},
				h4: {
					fontSize: theme('fontSize.xl'),
					fontWeight: theme('fontWeight.semibold'),
				},
				h5: {
					fontSize: theme('fontSize.lg'),
					fontWeight: theme('fontWeight.medium'),
				},
				h6: {
					fontSize: theme('fontSize.base'),
					fontWeight: theme('fontWeight.medium'),
				},
			});

			// Add custom component classes
			addComponents({
				'.btn': {
					padding: theme('spacing.2') + ' ' + theme('spacing.4'),
					borderRadius: theme('borderRadius.md'),
					fontWeight: theme('fontWeight.medium'),
					transitionProperty: 'all',
					transitionDuration: '150ms',
					'&:focus': {
						outline: '2px solid transparent',
						outlineOffset: '2px',
						'--tw-ring-offset-shadow':
							'var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)',
						'--tw-ring-shadow':
							'var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color)',
						boxShadow:
							'var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)',
					},
				},
				'.btn-primary': {
					backgroundColor: theme('colors.primary.600'),
					color: theme('colors.white'),
					'&:hover': {
						backgroundColor: theme('colors.primary.700'),
					},
				},
				'.btn-secondary': {
					backgroundColor: theme('colors.secondary.600'),
					color: theme('colors.white'),
					'&:hover': {
						backgroundColor: theme('colors.secondary.700'),
					},
				},
			});

			// Add custom utilities
			addUtilities({
				'.text-balance': {
					textWrap: 'balance',
				},
				'.text-pretty': {
					textWrap: 'pretty',
				},
			});
		}),
	],
};
