<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="test" xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/squizlabs/PHP_CodeSniffer/master/phpcs.xsd">

	<description>A set of rules to check for a custom WordPress theme</description>
	<!-- Based on the `phpcs.xml.dist.sample` file from the WordPress Coding Standards. -->

	<!-- Pass some flags to PHPCS:
		 p flag: Show progress of the run.
		 s flag: Show sniff codes in all reports.
	-->
	<arg value="ps"/>

	<!-- Strip the file paths down to the relevant bit. -->
	<arg name="basepath" value="./"/>

	<!-- Check up to 8 files simultaneously. -->
	<arg name="parallel" value="8"/>

	<!-- Check PHP files only. JavaScript and CSS files are checked separately. -->
	<arg name="extensions" value="php"/>

	<!-- Check all files in this directory and the directories below it. -->
	<file>.</file>

	<!-- Exclude patterns. -->
	<exclude-pattern>/vendor/*</exclude-pattern>
	<exclude-pattern>/node_modules/*</exclude-pattern>

	<!-- Include the WordPress-Extra standard. -->
	<rule ref="WordPress-Extra">
		<!--
		We may want a middle ground, though. The best way to do this is to add
		the entire ruleset, then remove rules that don't suit a project. We can
		do this by running `phpcs` with the '-s' flag, which allows us to see
		the names of the sniffs reporting errors.

		Once we know the sniff names, we can opt to exclude sniffs which don't
		suit our project.

		The examples below demonstrate how you can exclude rules. They are not
		intended as advice about which sniffs to exclude.
		-->

		<!--
		<exclude name="WordPress.WhiteSpace.ControlStructureSpacing"/>
		<exclude name="WordPress.Security.EscapeOutput"/>
		-->
	</rule>

	<!-- Let's also check that everything is properly documented. -->
	<rule ref="WordPress-Docs"/>

	<!-- Check for PHP cross-version compatibility. -->
	<config name="testVersion" value="7.4-"/>
	<rule ref="PHPCompatibilityWP"/>

	<!--
	To get the optimal benefits of using the WordPress Coding Standards, we
	should add a couple of custom properties.

	For information on additional custom properties available, check out
	the wiki:
	https://github.com/WordPress/WordPress-Coding-Standards/wiki/Customizable-sniff-properties
	-->
	<config name="minimum_supported_wp_version" value="6.2"/>

	<rule ref="WordPress.WP.I18n">
		<properties>
			<property name="text_domain" type="array">
				<element value="test"/>
			</property>
		</properties>
	</rule>

	<rule ref="WordPress.NamingConventions.PrefixAllGlobals">
		<properties>
			<property name="prefixes" type="array">
				<element value="test"/>
			</property>
		</properties>
	</rule>

	<rule ref="WordPress.Files.FileName">
		<properties>
			<property name="is_theme" value="true"/>
		</properties>
	</rule>

</ruleset>
