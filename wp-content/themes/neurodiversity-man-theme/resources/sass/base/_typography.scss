/* ASAP Font */
@font-face {
  font-family: ASAP;
  src: url('../fonts/Asap-Regular.woff2') format('woff2'),
       url('../fonts/Asap-Regular.woff') format('woff'),
       url('../fonts/Asap-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: ASAP;
  src: url('../fonts/Asap-Medium.woff2') format('woff2'),
       url('../fonts/Asap-Medium.woff') format('woff'),
       url('../fonts/Asap-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: ASAP;
  src: url('../fonts/Asap-SemiBold.woff2') format('woff2'),
       url('../fonts/Asap-SemiBold.woff') format('woff'),
       url('../fonts/Asap-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: ASAP;
  src: url('../fonts/Asap-Bold.woff2') format('woff2'),
       url('../fonts/Asap-Bold.woff') format('woff'),
       url('../fonts/Asap-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* Italic variants */
@font-face {
  font-family: ASAP;
  src: url('../fonts/Asap-Italic.woff2') format('woff2'),
       url('../fonts/Asap-Italic.woff') format('woff'),
       url('../fonts/Asap-Italic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}