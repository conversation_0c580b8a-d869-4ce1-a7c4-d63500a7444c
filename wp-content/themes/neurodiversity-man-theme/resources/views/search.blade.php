@extends('layouts.app')

@section('content')
	@php global $wp_query; @endphp
	<section id="search-results" class="alignfull py-8 antialiased">
		<div class="mx-auto max-w-screen-lg px-4 2xl:px-0">
			<div class="lg:flex lg:items-center lg:justify-between lg:gap-4">
				<h2 class="shrink-0 text-xl font-semibold text-gray-900 sm:text-2xl">Results
					({{ $wp_query->post_count }})</h2>

				<form class="mt-4 w-full gap-4 sm:flex sm:items-center sm:justify-end lg:mt-0"
					  action="{{ home_url( '/' ) }}" method="get">
					<label for="default-search"
						   class="mb-2 text-sm font-medium text-gray-900 sr-only">{{ __( 'Search for:' ) }}</label>
					<div class="relative w-full flex-1 lg:max-w-sm">
						<div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
							<svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
								 fill="none" viewBox="0 0 20 20">
								<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
									  stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
							</svg>
						</div>
						<input name="s" type="search" id="default-search"
							   class="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500"
							   placeholder="Search..." value="{{  get_search_query() }}" required/>
						<button type="submit"
								class="text-white absolute end-2.5 bottom-2.5 bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2">{{ esc_attr__( 'Search' ) }}</button>
					</div>
				</form>
			</div>
			<div class="results mt-6 flow-root">
				<div class="-my-6 divide-y divide-gray-200">
					@if (! have_posts())
						<div class="space-y-4 py-6 md:py-8">
							<div class="grid gap-4">
								<p class="text-xl font-semibold text-gray-900 hover:underline">No Results Found</p>
							</div>
							<p class="text-base font-normal text-gray-500">We are unable to find any results to match
								your criteria.</p>
						</div>
					@else
						@while(have_posts())
							@php(the_post())
							@include('partials.content-search')
						@endwhile
					@endif
				</div>
			</div>
			<div class="mt-6">
				{!! get_the_posts_navigation() !!}
			</div>
		</div>
	</section>

@endsection
