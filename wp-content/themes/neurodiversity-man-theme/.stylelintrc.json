{"extends": ["stylelint-config-standard-scss"], "rules": {"at-rule-no-unknown": [true, {"ignoreAtRules": ["tailwind", "apply", "variants", "responsive", "screen", "layer", "use"]}], "scss/at-rule-no-unknown": [true, {"ignoreAtRules": ["tailwind", "apply", "variants", "responsive", "screen", "layer"]}], "no-descending-specificity": null, "selector-class-pattern": null, "selector-id-pattern": null, "scss/operator-no-newline-after": null, "block-no-empty": null, "no-invalid-position-at-import-rule": null}, "ignoreFiles": ["build/**/*", "vendor/**/*", "node_modules/**/*", "**/*.min.css", "**/*.js", "**/*.php", "**/*.blade.php", "style.css"]}