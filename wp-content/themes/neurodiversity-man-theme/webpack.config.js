const path = require('path');
const fs = require('fs');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const ImageMinimizerPlugin = require('image-minimizer-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const webpack = require('webpack');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const { ThemeJsonPlugin } = require('theme-json-generator');
const WrapCssPlugin = require('./WrapCssPlugin');
const glob = require('glob');
const BrowserSyncPlugin = require('browser-sync-v3-webpack-plugin');

const isProduction = process.env.NODE_ENV === 'production';
const isDevelopment = !isProduction;
const JS_DIR = path.resolve(__dirname, 'resources/js');
const SASS_DIR = path.resolve(__dirname, 'resources/sass');
const BUILD_DIR = path.resolve(__dirname, 'build');

// Paths
const blocksScssPath = path.join(SASS_DIR, 'components/blocks.scss');
const blocksJSPath = path.join(JS_DIR, 'blocks.js');

// Dynamically import all block SCSS files
const blockScssFiles = glob
	.sync('app/Blocks/*/src/sass/*.scss')
	.map((file) => `@use '${path.relative(SASS_DIR, '../' + file)}';`)
	.join('\n');

fs.writeFileSync(blocksScssPath, blockScssFiles + '\n');

// Dynamically import all block JS files
const blockJSFiles = glob
	.sync('app/Blocks/*/src/scripts/*.js')
	.map((file) => `import '${path.relative(JS_DIR, file)}'`)
	.join('\n');

fs.writeFileSync(blocksJSPath, blockJSFiles + '\n');

const entry = {
	app: path.join(JS_DIR, 'app.js'),
};

const output = {
	path: BUILD_DIR,
	filename: 'js/[name].js',
};

const getPlugins = () => {
	const plugins = [
		new MiniCssExtractPlugin({
			filename: 'css/[name].css',
		}),
		new CopyWebpackPlugin({
			patterns: [
				{
					from: path.resolve(__dirname, 'resources/images'),
					to: path.resolve(BUILD_DIR, 'images/[name][ext]'),
				},
			],
		}),
		new ThemeJsonPlugin({
			from: './theme.config.js',
			to: './theme.json',
			version: 3,
			scheme: 'https://schemas.wp.org/trunk/theme.json',
		}),
	];

	if (isDevelopment) {
		plugins.push(
			new BrowserSyncPlugin(
				{
					proxy: 'https://neurodiversity-man.ddev.site',
					host: 'neurodiversity-man.ddev.site',
					port: 3000,
					open: 'external',
					https: {
						key: '../../../.ddev/traefik/certs/neurodiversity-man.key',
						cert: '../../../.ddev/traefik/certs/neurodiversity-man.crt',
					},
					notify: false,
					files: ['**/*.php', 'build/**/*.css', 'build/**/*.js'],
					ghostMode: false,
					watchOptions: {
						ignoreInitial: true,
					},
					socket: {
						port: 443,
					},
				},
				{
					reload: false,
					injectCss: true,
				}
			)
		);
	}

	return plugins;
};

const moduleRules = [
	{
		test: /\.scss$/,
		exclude: /node_modules/,
		use: [
			MiniCssExtractPlugin.loader,
			'css-loader',
			{
				loader: 'postcss-loader',
				options: {
					postcssOptions: {
						plugins: [
							require('tailwindcss')('./tailwind.config.js'),
							require('autoprefixer'),
						],
					},
				},
			},
			{
				loader: 'sass-loader',
				options: {
					implementation: require('sass'),
					sassOptions: {
						outputStyle: isProduction ? 'compressed' : 'expanded',
					},
				},
			},
		],
	},
	{
		test: /\.css$/,
		use: [
			MiniCssExtractPlugin.loader,
			'css-loader',
			{
				loader: 'postcss-loader',
				options: {
					postcssOptions: {
						plugins: [
							require('tailwindcss')('./tailwind.config.js'),
							require('autoprefixer'),
						],
					},
				},
			},
		],
	},
	{
		test: /\.(woff(2)?|eot|ttf|otf|svg)$/,
		type: 'asset/resource',
		generator: {
			filename: 'fonts/[name][ext][query]',
		},
	},
	{
		test: /\/images\/.*\.(png|jpg|jpeg|gif|webp|svg)$/i,
		type: 'asset/resource',
		generator: {
			filename: 'images/[name][ext][query]',
		},
	},
];

const optimizationConfig = {
	minimizer: [
		new TerserPlugin({
			extractComments: false,
		}),
		new CssMinimizerPlugin(),
		new ImageMinimizerPlugin({
			minimizer: {
				implementation: ImageMinimizerPlugin.sharpMinify,
				options: {
					encodeOptions: {
						jpeg: {
							quality: 100,
						},
						webp: {
							lossless: true,
						},
						avif: {
							lossless: true,
						},
						png: {},
						gif: {},
					},
				},
			},
		}),
	],
};

module.exports = {
	entry,
	output,
	module: {
		rules: moduleRules,
	},
	target: isProduction ? 'browserslist' : 'web',
	devtool: isProduction ? false : 'source-map',
	plugins: getPlugins(),
	optimization: optimizationConfig,
	resolve: {
		alias: {
			fonts: path.resolve(__dirname, 'resources/fonts'),
			images: path.resolve(__dirname, 'resources/images'),
		},
	},
};
