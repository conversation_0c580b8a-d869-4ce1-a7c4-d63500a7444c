{"singleQuote": true, "trailingComma": "all", "useTabs": true, "tabWidth": 4, "printWidth": 100, "bracketSpacing": true, "arrowParens": "always", "endOfLine": "lf", "semi": true, "plugins": ["prettier-plugin-tailwindcss"], "tailwindConfig": "./tailwind.config.js", "overrides": [{"files": "*.php", "options": {"phpVersion": "8.0", "printWidth": 120, "tabWidth": 4, "useTabs": false, "singleQuote": false, "trailingCommaPHP": true}}, {"files": "*.blade.php", "options": {"parser": "php", "printWidth": 120}}, {"files": ["*.json", "*.yml", "*.yaml"], "options": {"tabWidth": 2, "useTabs": false}}, {"files": "*.md", "options": {"proseWrap": "always", "printWidth": 80}}]}