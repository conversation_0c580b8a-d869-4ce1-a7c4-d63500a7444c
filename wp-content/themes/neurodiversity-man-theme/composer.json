{"name": "kyle-rogers/neurodiversity-man", "description": "A modern WordPress theme built with BladeOne and TailwindCSS", "keywords": ["blade", "template", "view", "php", "wordpress", "boilerplate", "templating"], "require": {"php": ">=8.0", "ext-json": "*", "illuminate/support": "^10.0 || ^11.0", "illuminate/view": "^10.0 || ^11.0", "illuminate/config": "^10.0 || ^11.0", "illuminate/container": "^10.0 || ^11.0"}, "suggest": {"ext-mbstring": "This extension is used if it's active", "eftec/bladeonehtml": "Extension to create forms"}, "autoload": {"psr-4": {"NeurodiversityMan\\": "app"}}, "autoload-dev": {"psr-4": {"NeurodiversityMan\\Tests\\": "tests"}}, "bin": ["app/Core/BladeOne/bladeonecli"], "require-dev": {"wp-cli/wp-cli": "^2.11", "phpunit/phpunit": "^10.5 || ^11.0", "php-stubs/wordpress-stubs": "^6.7", "wp-coding-standards/wpcs": "^3.1", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "squizlabs/php_codesniffer": "^3.11", "php-stubs/acf-pro-stubs": "^6.3", "phpstan/phpstan": "^1.12", "szepeviktor/phpstan-wordpress": "^1.3", "mockery/mockery": "^1.6", "symfony/var-dumper": "^6.4 || ^7.0"}, "scripts": {"phpcs": "phpcs --standard=phpcs.xml.dist", "phpcbf": "phpcbf --standard=phpcs.xml.dist", "phpstan": "phpstan analyse --memory-limit=1G", "test": "phpunit", "test:coverage": "phpunit --coverage-html coverage", "lint": ["@phpcs", "@phpstan"], "lint:fix": "@phpcbf", "post-install-cmd": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-update-cmd": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}, "optimize-autoloader": true, "sort-packages": true}}