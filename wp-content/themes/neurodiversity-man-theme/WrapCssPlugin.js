const fs = require('fs');
const path = require('path');

class WrapCssPlugin {
	constructor(options) {
		this.options = options;
		this.hasProcessed = false;
		this.retryCount = 0;
	}

	apply(compiler) {
		compiler.hooks.afterEmit.tapAsync(
			'WrapCssPlugin',
			(compilation, callback) => {
				if (this.hasProcessed) {
					return callback();
				}

				const editorScssPath = path.resolve(
					this.options.srcPath,
					'editor.scss'
				);
				const appCssPath = path.resolve(
					this.options.path,
					'css/app.css'
				);
				const adminScssPath = path.resolve(
					this.options.srcPath,
					'admin.scss'
				);

				const checkFiles = () => {
					const appExists = fs.existsSync(appCssPath);
					const adminExists = fs.existsSync(adminScssPath);

					if (appExists && adminExists) {
						fs.readFile(
							appCssPath,
							'utf8',
							(err, appCssContent) => {
								if (err) {
									return callback(err);
								}

								fs.readFile(
									adminScssPath,
									'utf8',
									(err, adminScssContent) => {
										if (err) {
											return callback(err);
										}
										appCssContent = appCssContent.replace(
											/@import\s+["']\.\/fonts["'];/g,
											''
										);

										const mergedCss = `
${adminScssContent}
${appCssContent}
`;

										const wrappedCss = `
.block-editor-iframe__body,
.edit-site-visual-editor__editor-canvas,
.editor-styles-wrapper .acf-block-preview{
${mergedCss}
}`;

										fs.unlink(editorScssPath, (err) => {
											if (err) {
												return callback(err);
											}
											fs.writeFile(
												editorScssPath,
												wrappedCss,
												'utf8',
												(err) => {
													if (err) {
														return callback(err);
													}
													this.hasProcessed = true;
													callback();
												}
											);
										});
									}
								);
							}
						);
					} else {
						if (!this.retryCount) {
							this.retryCount = 0;
						}

						this.retryCount++;

						if (this.retryCount < 50) {
							setTimeout(checkFiles, 100);
						} else {
							console.warn(
								'WrapCssPlugin: Could not find required files after multiple attempts. Skipping.'
							);
							this.hasProcessed = true;
							callback();
						}
					}
				};

				checkFiles();
			}
		);
	}
}

module.exports = WrapCssPlugin;
