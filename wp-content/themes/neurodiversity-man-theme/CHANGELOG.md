# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Initial release of WordPress Blade Boilerplate
- BladeOne templating engine integration
- TailwindCSS v3.4+ with modern configuration
- ACF Framework for structured field management
- Custom Gutenberg block development system
- WP-CLI commands for theme setup and block scaffolding
- Interactive theme setup script (`npm run theme:setup`)
- Modern build system with Webpack 5
- Hot module replacement and browser sync
- Comprehensive code quality tools (ESLint, Prettier, StyleLint, PHPCS, PHPStan)
- Git hooks with <PERSON><PERSON> and lint-staged
- Automated image optimization
- Custom post type registration helper
- Environment-based configuration with .env support
- Translation-ready with POT file generation
- Extensive documentation and examples

### Changed
- Updated all npm dependencies to latest stable versions
- Updated PHP dependencies to support PHP 8.0+
- Modernized webpack configuration with better optimization
- Improved file structure with clear separation of core/custom code
- Enhanced development workflow with better error handling

### Security
- Updated all dependencies to patch known vulnerabilities
- Implemented WordPress coding standards
- Added security best practices to boilerplate code

## [3.0.0] - Previous Version

### Added
- Basic BladeOne integration
- TailwindCSS setup
- ACF block support
- Basic webpack configuration

### Changed
- Updated to support WordPress 6.0+
- Improved build process

### Fixed
- Various bug fixes and improvements

[Unreleased]: https://github.com/your-repo/wordpress-blade-theme/compare/v4.0.0...HEAD
[4.0.0]: https://github.com/your-repo/wordpress-blade-theme/compare/v3.0.0...v4.0.0
[3.0.0]: https://github.com/your-repo/wordpress-blade-theme/releases/tag/v3.0.0 