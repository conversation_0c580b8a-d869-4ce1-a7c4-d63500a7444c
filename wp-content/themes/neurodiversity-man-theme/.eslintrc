{"root": true, "ignorePatterns": ["*.min.js"], "overrides": [{"files": ["*.js"], "extends": ["eslint:recommended", "prettier"], "env": {"browser": true}, "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}}, {"files": ["*.blade.php"], "parser": "@angular-eslint/template-parser", "plugins": ["php-markup", "tailwindcss"], "extends": ["plugin:tailwindcss/recommended"], "rules": {"tailwindcss/migration-from-tailwind-2": "off", "tailwindcss/no-custom-classname": ["warn", {"config": "./tailwind.config.js", "whitelist": ["(page|entry|comment)\\-([-a-z]*)", "vcard", "search-form", "dt-published", "p-author", "h-card"]}]}, "settings": {"tailwindcss": {"config": "./tailwind.config.js", "cssFiles": ["./assets/**/*.scss"]}}}]}