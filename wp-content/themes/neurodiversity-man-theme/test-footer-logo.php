<?php
/**
 * Alternative Footer Logo Implementation
 * This is a backup approach using a different method
 */

// Only for admins
if (!current_user_can('manage_options')) {
    return;
}

// Alternative approach using direct WordPress customizer API
add_action('customize_register', function($wp_customize) {
    // Remove any existing footer logo setting to avoid conflicts
    $wp_customize->remove_setting('custom_footer_logo');
    $wp_customize->remove_control('custom_footer_logo');
    
    // Add a new setting with a different name
    $wp_customize->add_setting('footer_logo_alt', [
        'capability' => 'edit_theme_options',
        'default' => '',
        'transport' => 'refresh',
        'sanitize_callback' => function($input) {
            // Simple validation
            if (empty($input)) {
                return '';
            }
            
            // Log for debugging
            error_log('Footer logo alt sanitize input: ' . $input);
            
            // Check if it's a valid attachment
            if (is_numeric($input) && wp_attachment_is_image($input)) {
                error_log('Footer logo alt: Valid image ID ' . $input);
                return absint($input);
            }
            
            error_log('Footer logo alt: Invalid input, returning empty');
            return '';
        },
        'type' => 'theme_mod',
    ]);

    // Add control
    $wp_customize->add_control(
        new WP_Customize_Image_Control(
            $wp_customize,
            'footer_logo_alt',
            [
                'label' => __('Footer Logo (Alternative)', 'neurodiversity-man'),
                'section' => 'title_tagline',
                'priority' => 7,
                'description' => __('Alternative footer logo upload - testing different approach.', 'neurodiversity-man'),
                'settings' => 'footer_logo_alt',
            ]
        )
    );
}, 20); // Higher priority to run after the main customizer

// Add a simple admin page to test the values
add_action('admin_menu', function() {
    add_submenu_page(
        'themes.php',
        'Footer Logo Test',
        'Footer Logo Test',
        'manage_options',
        'footer-logo-test',
        function() {
            echo '<div class="wrap">';
            echo '<h1>Footer Logo Test Results</h1>';
            
            $original = get_theme_mod('custom_footer_logo');
            $alternative = get_theme_mod('footer_logo_alt');
            $test_text = get_theme_mod('footer_logo_test');
            
            echo '<table class="widefat">';
            echo '<tr><th>Setting</th><th>Value</th><th>Type</th><th>Status</th></tr>';
            
            echo '<tr>';
            echo '<td>custom_footer_logo</td>';
            echo '<td>' . var_export($original, true) . '</td>';
            echo '<td>' . gettype($original) . '</td>';
            echo '<td>' . (empty($original) ? 'EMPTY' : 'HAS VALUE') . '</td>';
            echo '</tr>';
            
            echo '<tr>';
            echo '<td>footer_logo_alt</td>';
            echo '<td>' . var_export($alternative, true) . '</td>';
            echo '<td>' . gettype($alternative) . '</td>';
            echo '<td>' . (empty($alternative) ? 'EMPTY' : 'HAS VALUE') . '</td>';
            echo '</tr>';
            
            echo '<tr>';
            echo '<td>footer_logo_test</td>';
            echo '<td>' . var_export($test_text, true) . '</td>';
            echo '<td>' . gettype($test_text) . '</td>';
            echo '<td>' . (empty($test_text) ? 'EMPTY' : 'HAS VALUE') . '</td>';
            echo '</tr>';
            
            echo '</table>';
            
            echo '<h2>All Theme Mods</h2>';
            echo '<pre style="background: #f9f9f9; padding: 10px; max-height: 400px; overflow-y: auto;">';
            print_r(get_theme_mods());
            echo '</pre>';
            
            echo '<h2>Direct Database Check</h2>';
            $theme_name = get_option('stylesheet');
            $db_mods = get_option("theme_mods_$theme_name");
            echo '<pre style="background: #f9f9f9; padding: 10px; max-height: 400px; overflow-y: auto;">';
            print_r($db_mods);
            echo '</pre>';
            
            echo '<p><a href="' . admin_url('customize.php') . '" class="button button-primary">Go to Customizer</a></p>';
            echo '</div>';
        }
    );
});

// Helper functions for the alternative approach
function get_footer_logo_alt() {
    $logo_id = get_theme_mod('footer_logo_alt');
    
    if (empty($logo_id)) {
        return false;
    }
    
    return wp_get_attachment_image($logo_id, 'full', false, [
        'class' => 'footer-logo-alt',
        'alt' => get_bloginfo('name') . ' Footer Logo'
    ]);
}

function get_footer_logo_alt_url() {
    $logo_id = get_theme_mod('footer_logo_alt');
    
    if (empty($logo_id)) {
        return false;
    }
    
    return wp_get_attachment_image_url($logo_id, 'full');
}
