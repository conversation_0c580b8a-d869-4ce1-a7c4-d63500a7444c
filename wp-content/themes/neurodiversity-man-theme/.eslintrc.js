module.exports = {
	root: true,
	extends: [
		'plugin:@wordpress/eslint-plugin/recommended',
		'plugin:tailwindcss/recommended',
		'prettier',
	],
	parser: '@babel/eslint-parser',
	parserOptions: {
		requireConfigFile: false,
		ecmaVersion: 2022,
		sourceType: 'module',
		ecmaFeatures: {
			jsx: true,
		},
	},
	env: {
		browser: true,
		es6: true,
		jquery: true,
		node: true,
	},
	globals: {
		wp: 'readonly',
		jQuery: 'readonly',
		$: 'readonly',
		acf: 'readonly',
	},
	rules: {
		// Tailwind CSS specific rules
		'tailwindcss/no-custom-classname': 'off',
		'tailwindcss/classnames-order': 'warn',

		// WordPress specific adjustments
		'@wordpress/no-global-event-listener': 'off',

		// General rules
		'no-console': ['warn', { allow: ['warn', 'error'] }],
		'no-debugger': 'error',
		'no-unused-vars': [
			'error',
			{
				vars: 'all',
				args: 'after-used',
				ignoreRestSiblings: true,
			},
		],
		'prefer-const': 'error',
		'no-var': 'error',
		'object-shorthand': 'error',
		'prefer-template': 'error',
		'prefer-arrow-callback': 'error',
		'arrow-body-style': ['error', 'as-needed'],

		// Spacing and formatting (handled by Prettier mostly)
		'comma-dangle': ['error', 'always-multiline'],
		quotes: ['error', 'single', { avoidEscape: true }],
		semi: ['error', 'always'],
	},
	settings: {
		tailwindcss: {
			callees: ['clsx', 'cn', 'classnames'],
			config: 'tailwind.config.js',
		},
	},
	overrides: [
		{
			files: ['*.blade.php', '*.php'],
			processor: 'php-markup/php',
			rules: {
				// PHP-specific rule adjustments
				'no-undef': 'off',
			},
		},
		{
			files: ['webpack.*.js', '*.config.js'],
			env: {
				node: true,
			},
		},
	],
};
