<?php
# Exit if accessed directly

use NeurodiversityMan\Core\BladeOne\BladeOne;
use NeurodiversityMan\Core\Plugins\Blade;

if (!defined('ABSPATH')) {
	exit;
}

/*
|--------------------------------------------------------------------------
| Register The Auto Loader
|--------------------------------------------------------------------------
|
| Composer provides a convenient, automatically generated class loader for
| our theme. We will simply require it into the script here so that we
| don't have to worry about manually loading any of our classes later on.
|
*/

if (!file_exists($composer = get_theme_file_path() . '/vendor/autoload.php')) {
	wp_die(__('Error locating autoloader. Please run <code>composer install</code>.', 'neurodiversity-man'));
}
require $composer;

/*
|--------------------------------------------------------------------------
| Register JBC Theme Files
|--------------------------------------------------------------------------
|
| Out of the box, JBC ships with categorically named theme files
| containing common functionality and setup to be bootstrapped with your
| theme. Simply add (or remove) files from the array below to change what
| is registered alongside Sage.
|
*/
if (!defined('JBC_ENV')) {
	define('JBC_ENV', get_option('jbc_options')['jbc_field_env'] ?? isLocal() ? 'local' : 'dev');
}
if (!defined('JBC_VERSION')) {
	define('JBC_VERSION', wp_get_theme()->get('Version'));
}
if (!defined('JBC_DIR')) {
	define('JBC_DIR', get_stylesheet_directory_uri());
}
if (!defined('JBC_PATH')) {
	define('JBC_PATH', get_stylesheet_directory());
}
if (!defined('WP_BLADEONE_VIEWS')) {
	define('WP_BLADEONE_VIEWS', get_template_directory() . '/resources/views');
}
if (!defined('WP_BLADEONE_COMPONENTS')) {
	define('WP_BLADEONE_COMPONENTS', get_template_directory() . '/app/Components');
}

if (!defined('WP_BLADEONE_CACHE')) {
	define('WP_BLADEONE_CACHE', WP_CONTENT_DIR . '/cache/.wp-bladeone-cache');
}

if (!file_exists(WP_BLADEONE_CACHE)) {
	wp_mkdir_p(WP_BLADEONE_CACHE);
}

if (!defined('WP_BLADEONE_MODE')) {
	define('WP_BLADEONE_MODE', BladeOne::MODE_AUTO);
}
collect(['setup', 'filters', 'helpers'])
	->each(function ($file) {
		if (!locate_template($file = "app/{$file}.php", true, true)) {
			wp_die(
			/* translators: %s is replaced with the relative file path */
				sprintf(__('Error locating <code>%s</code> for inclusion.', 'neurodiversity-man'), $file)
			);
		}
	});


function wp_bladeone()
{
	static $instance;

	if (null === $instance) {
		$instance = new Blade();
	}

	return $instance;
}

function wp_bladeone_blocks()
{
	static $instance;

	if (null === $instance) {
		$instance = new Blade();
	}

	return $instance;
}

function wp_bladeone_components()
{
	static $instance;

	if (null === $instance) {
		$instance = new Blade();
	}

	return $instance;
}

function isLocal(): bool
{
	$whitelist = [
		'127.0.0.1',
		'::1',
	];
	if (in_array($_SERVER['REMOTE_ADDR'], $whitelist)) {
		return true;
	}

	return false;
}


add_action('pre_get_posts', function ($query) {
	$query->set('posts_per_page', '');
	if (isset($_REQUEST['s']) && !is_admin() && $query->is_main_query()) {
		$allowed_post_types = acf_maybe_unserialize(get_option('options_global_settings_search_page_searchable_post_types'));
		if (isset($_REQUEST['post_type'])) {
			$query->set('post_type', $_REQUEST['post_type']); // Add any other post-types you want to include in the search
		} else {
			$query->set('post_type', $allowed_post_types); // Add any other post-types you want to include in the search
		}
	}
});
