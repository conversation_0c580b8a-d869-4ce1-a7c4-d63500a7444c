=== Formidable Forms - Contact Form Plugin, Survey, Quiz, Payment, Calculator Form & Custom Form Builder ===
Plugin Name: Formidable Forms - Contact Form, Survey & Quiz Form Builder for WordPress
Contributors: formidableforms, sswells, srwells
Tags: forms, form builder, survey, payment form, custom form, contact form, form maker, form creator, paypal, stripe, stripe form, quote form, contact button, form manager, free, survey form, email subscription, donation form, user registration form, wordpress registration, feedback form, contact form plugin, wordpress form plugin, lead form, registration form, contact form builder
Requires at least: 5.2
Tested up to: 6.8.1
Requires PHP: 7.0
Stable tag: 6.21.1

The most advanced WordPress forms plugin. Go beyond contact forms with our drag and drop form builder for surveys, quizzes, and more.

== Description ==

== Unleash the Most Powerful WordPress Form Builder Plugin on the Market ==
Discover [Formidable Forms](https://formidableforms.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion), the trailblazing solution-centric WordPress form plugin, crafted to revolutionize form creation. Our intuitive drag-and-drop interface enables you to effortlessly design online forms like surveys, polls, quizzes, registration, payment, lead, email forms, and calculator forms - the possibilities are boundless.

https://youtu.be/7X2BqhRsXcg

[Watch the video overview for the Formidable form, calculator, and quiz builder](https://youtu.be/7X2BqhRsXcg)

At Formidable, we prioritize unparalleled limit-breaking. We challenge conventional boundaries unlike any other WP form maker plugin, providing you with the tools to rapidly construct intricate custom forms and applications.

We're obsessed with efficiency. Formidable is fine-tuned for outstanding speed and optimal server performance, making it one of the FASTEST WordPress form builders available today.

You can start with pre-built templates or create totally custom forms from scratch. All with an easy-to-use drag and drop form maker interface.

> <strong>Formidable Pro</strong>
> This plugin is the free version of Formidable Pro with advanced options like an email subscription form, multi-page form, file upload form, quiz grading, or a smart form with conditional logic. Stack on repeater fields, payment integrations, form templates, relationships, and cascading dropdown fields. Don't forget the calculated fields, front-end form editing, and powerful web application builder.
>
> Formidable Pro transcends traditional contact form plugins. [Upgrade to the most sophisticated premium WordPress forms plugin now!](https://formidableforms.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion)
>
> Or start with the [Lite vs Pro comparison](https://formidableforms.com/knowledgebase/what-is-the-difference-between-the-lite-free-and-pro-version/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion).

Embrace the limitless possibilities for crafting a lead form, poll, subscription form, request a quote form, payment form, user registration form, and beyond. Dubbed the Swiss Army Knife of WordPress, Formidable integrates seamlessly into your tech ecosystem.

== Effortless Drag & Drop Form Maker ==

Harness the power of Formidable's drag and drop form creator, equipped with essential fields for crafting solution-oriented WP forms fast. Build unlimited quiz forms, price calculators, and more with ease.

== Robust Entry Management for Free Web Forms ==

Safeguard leads with entries stored directly in your WordPress database, accessible from the form creator back-end, ensuring no lead is lost. Our **GDPR-friendly** form generator offers options to toggle features like IP tracking, saving submissions, or GDPR consent fields.

Importing leads to services like Mailchimp is straightforward. Just export leads to CSV for use in Excel or any platform.

Enhance user experience with customizable email notifications, autoresponders, success messages, or redirects.

== The Only Form Maker Plugin with an Advanced Styler ==

Elevate website forms with Formidable's built-in styler. Tailor the custom form appearance to flawlessly blend with the site's design in just a few clicks. Embrace the ease of transforming online forms to reflect your brand’s essence.

== Craft Smart Surveys & Generate Insightful Reports ==

With Formidable Pro, access survey tools rivaling Survey Monkey — minus the hefty price tag. Design impactful poll forms featuring Net Promoter Scores (NPS), Likert scales, star ratings, and image choices with unparalleled simplicity.

Unlock the power of your data through easy analysis and beautiful reports. Visualize poll, quiz, and order form data through engaging graphs and reports, ready to interpret or tailor to your needs. Display these insights to visitors by embedding graphs in posts or pages.

== Enjoyable Creation of Advanced WordPress Registration Forms ==

Formidable transcends typical contact form plugin functionality by offering options like the repeater field, ideal for crafting comprehensive registration forms for sports teams, events, or retreats. Enhanced by robust marketing integrations and APIs, effortlessly funnel data wherever it serves you best.

== Seamless Payments and Credit Card Processing ==

Introducing sophisticated payment forms, donation forms, and other credit card forms is fast with integrations with leading payment services like PayPal, Stripe, and Authorize.net. A custom WooCommerce form with custom fields is straightforward, ensuring your eCommerce solutions are as versatile as they are powerful.

== Data-Driven Web Applications Made Easy ==

With Formidable Views, unlock the potential to display submitted data on the front-end, enabling dynamic, data-driven web applications including searchable databases. From real estate to job boards, Formidable empowers you to craft comprehensive employment listings, event calendars, business or member directories, and much more.

== Elevate Your Online Store with WooCommerce Product Order Forms ==

As the only WordPress form creator plugin fully integrated with WooCommerce, Formidable supercharges your online store.

Design intricate product order forms with custom calculation fields, effortlessly funneling data into the WooCommerce cart for dynamic pricing options, streamlining the shopping experience for your customers.

== Innovative Quiz and Calculator Forms ==

Beyond simple order forms, our quiz maker excels in creating engaging web calculators and quiz forms. From educational assessments to viral BuzzFeed-style quizzes, Formidable's robust features facilitate the creation, management, and display of interactive content that captivates and grows your audience.

== Sophisticated WordPress User Registration and Profile Forms ==

For WordPress membership sites, Formidable offers unparalleled customization for user registration and profile forms.

With the front-end editing capabilities of a custom profile form, users can maintain up-to-date profiles, contributing to a cohesive and user-friendly site experience. Each step, from initial lead form to final payment form, enriches the user profile.

== Advanced Fields and Features for Business Growth ==

Formidable's rich array of features include multi-page forms, save-and-continue options, cascading form fields, conditional logic, partial submissions, and invisible spam protection.

With powerful integration options for front-end user post submissions, calculated fields, quizzes, and user flow management, Formidable's form maker stands as an all-in-one solution for form creation and data management, eliminating the need for multiple plugins.

== A Developer’s Dream: Extend and Customize with Ease ==

Championed by developers, freelancers, and agencies alike, Formidable is the form creator that's celebrated for its extensibility and flexibility. From custom web app creation to sophisticated form designs, Formidable supports complex projects with low overhead. This makes it the premier choice for professionals looking to push the boundaries of what's possible with WordPress.

* **Responsive Design**: Formidable guarantees that your WordPress forms will look great and function flawlessly across all devices, thanks to its mobile-responsive design and flexible layout options. Ensure that everyone can use your intake form, consent form on any device.
* **Customization at Your Fingertips**: With the ability to [customize HTML](https://formidableforms.com/features/customize-form-html-wordpress/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion) (like Contact Form 7) while retaining the simplicity of drag-and-drop, Formidable offers the perfect balance of power and ease of use.
* **[Invisible SPAM protection](https://formidableforms.com/features/invisible-spam-protection/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion)**: Benefit from invisible, yet effective spam protection techniques, including honeypot, Akismet, captcha forms (Google reCAPTCHA, hCaptcha, Cloudflare Turnstile), and WordPress comment blacklist integration, ensuring your focus remains on genuine interactions.
* **Unmatched Support**: Our world-class support team is dedicated to helping you defy limits, tackling bigger projects, securing more clients, and accelerating your business growth.

Formidable Forms is not just a WordPress form plugin. It's a comprehensive solution aimed at empowering you to create, customize, and extend your WordPress capabilities beyond expectations to defy gravity.

== Explore the Ultimate WordPress Form Solution ==

> Formidable Forms is an awesome professional form builder. There is much to like about Formidable Forms. From the drag and drop interface to the many developer hooks for creating your own custom features, this tool suits the needs for absolute beginners to the most advanced developer and everyone in between.
> Victor Font (Digital Business Strategist)

Dive into the endless possibilities with Formidable, the WordPress form builder that goes beyond basic forms. Here's a glimpse of what you can achieve with our feature-packed plugin:

* **Effortless Form Building**: Utilize our [drag and drop form maker](https://formidableforms.com/features/drag-drop-form-builder/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion) to craft everything from a simple email form to comprehensive quizzes or complex online applications.
* **Data Display and Management**: Other WordPress form plugins only let you collect data. With [Formidable Views](https://formidableforms.com/features/display-form-data-views/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion), showcase data like never before. Create job boards, event calendars, and business directories directly from your form submissions.
* **Advanced File Uploads**: Our [multiple file upload feature](https://formidableforms.com/features/wordpress-multiple-file-upload-form/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion) simplifies adding documents, photos, and music to any form, perfect for a job application form or WordPress user profile form.
* **Multi-Step Forms**: Boost conversions with [multi-step forms](https://formidableforms.com/features/wordpress-multi-step-form/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion), complete with progress bars and conditional logic for a streamlined user experience.
* **Intelligent [Calculator Forms](https://formidableforms.com/wordpress-calculator-plugin/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion)**: From mortgage calculators to auto loan calculators and date calculator forms, Formidable offers dynamic solutions for instant quotes and price estimates.
* **Custom Styling**: Match your forms to your brand with our [visual style creator](https://formidableforms.com/features/wordpress-visual-form-styler/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion), enabling personalized colors, fonts, and layouts (including RTL forms) without any coding.
* **User-Generated Content**: Enable front-end post and page submissions, allowing users to contribute content directly through forms. Create an online journaling platform, member directory, classified ads, community recipes, and more.
* **Comprehensive Entry Management**: Manage, display, and edit form entries with ease, offering front-end editing capabilities for a seamless user experience.
* **Integration and Automation**: Sync custom field values with ACF forms (Advanced Custom Fields), automate actions with our Forms API, and enhance communication with email routing and autoresponders.
* **Accessibility and Compliance**: Ensure your forms are accessible to everyone with WCAG compliance, making your website more inclusive.
* **Template Library**: Jumpstart form creation with our [pre-built form templates](https://formidableforms.com/form-templates/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion), catering to a wide range of uses from feedback to event registration forms.

https://youtu.be/qkL8rhBRL9s

[How to Build an Instant Quote Form](https://youtu.be/qkL8rhBRL9s)

Formidable Pro stands as a developer's dream, offering unmatched flexibility and power to create, extend, and customize forms. Whether you're building simple forms or complex data-driven applications, Formidable provides all the tools and features you need to defy limits and elevate your WordPress site.

* **Flexible Form Building**: With [repeating fields](https://formidableforms.com/features/dynamically-add-form-fields/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion), [PDF creation](https://formidableforms.com/features/form-to-pdf-wordpress-plugin/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion), digital signatures, [cascading lookup fields](https://formidableforms.com/features/cascading-dropdown-lookup-field/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion), and outcome quiz builder options, crafting dynamic forms is seamless.
* **Intelligent Automation**: Automate form submissions responses with AI (ChatGPT), and engage users with advanced datepickers and dynamic field relationships for a smart and intuitive user experience.
* **Engagement Tools**: Boost interaction with star ratings, password fields with strength meters, and conditional logic.
* **Enhanced User Experience**: Support for partial submissions, [landing pages](https://formidableforms.com/landing-page-forms/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion), comprehensive data visualization with graphs and charts, [conversational forms](https://formidableforms.com/conversational-forms/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion) (one question at a time), and survey fields enhance engagement and retention.
* **Accessibility and Control**: Schedule forms, limit entries, and even white-label forms for a personalized admin experience.

Unleash the full potential of your WordPress site by upgrading to [Formidable Pro](https://formidableforms.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion), the ultimate tool for creating smart forms and full web applications.

== Enhanced Payment Forms, APIs, and Marketing Integrations ==

Formidable Pro isn't just a form builder; it's a gateway to optimizing websites with comprehensive integrations. Here's how you can supercharge WordPress forms:

* **[Stripe Forms](https://formidableforms.com/features/stripe-payments-for-wordpress/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion)**: Facilitate on-site Stripe payments with options for one-time or recurring payment forms and donation forms. Compatible with a wide range of payment methods (including ACH, Cash App, Klarna, iDeal) even in our Lite version.
* **[PayPal Forms](https://formidableforms.com/features/paypal-wordpress-payments/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion)**: Automate instant and recurring payments with PayPal, integrating calculations for seamless transactions.
* **[Authorize.net AIM](https://formidableforms.com/features/authorize-net-payments/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion)**: Process one-time payments in order forms and price calculators.
* **[WooCommerce product configurator](https://formidableforms.com/features/customizable-woocommerce-forms/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion)**: Customize WooCommerce forms with additional fields and variable pricing.
* **Email Marketing Integrations**: Enhance your marketing efforts with direct integrations for **Mailchimp**, **Constant Contact**, **AWeber**, **MailPoet**, **Salesforce**, **ActiveCampaign**, **HubSpot**, and **GetResponse**, streamlining your lead collection and nurturing processes.
* **SMS and CRM**: With **Twilio** for SMS notifications or voting and a host of CRM integrations, staying connected with your audience has never been easier.
* **Multilingual Forms**: Extend your global reach with **WPML** and **Polylang** for multilingual forms.
* **[Zapier](https://formidableforms.com/features/form-entry-routing-with-zapier/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion)**: Unlock endless possibilities by connecting your web forms with over a thousand services for automation that simplifies your workflow.
* **Styling and Modals**: Apply **Bootstrap styling** to your forms for a polished look and leverage **Bootstrap modals** for engaging pop-up content.

Discover why Formidable Pro is hailed as the most advanced WordPress form plugin available. To access these powerful features and more, [upgrade to Pro](https://formidableforms.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion) today and redefine what you can achieve with WordPress forms.

== Credits ==
This online form and quiz builder plugin is created by Steve and Steph Wells and the amazing Strategy11 Team with <a href="https://syedbalkhi.com/">Syed Balkhi</a> as an Advisor.

== Installation ==
1. Go to the Plugins -> 'Add New' page in your WP admin area
2. Search for 'Formidable'
3. Click the 'Install Now' button, then 'Activate'
4. Go to the newly added 'Formidable' menu
5. Click the 'Add New' button to go to the form generator page and create a new email form
6. Insert your newly created lead form, quiz, or survey form on a page or post using the Embed Form pop up. Or insert it manually or into a widget using a shortcode [formidable id=x]. Alternatively use `<?php echo FrmFormsController::show_form(2); ?>` to add it in a theme file.

== Screenshots ==
1. Build a professional WP form without any code.
2. Page for creating a quote form, Stripe payment form, quiz form, and many more.
3. Field Options and CSS Layout Classes
4. Field Options for checkbox fields
5. View, create, edit, and delete entries on the back end from a to-do list, employment application form, and more.
6. Add a form widget into a sidebar

== Frequently Asked Questions ==
= How do I get started with the best forms for WordPress? =
The fastest way to build a form is to use the example we built for you. After you activate Formidable, insert [formidable id=contact-form] on the WordPress page of your choice.

Go to the Formidable page and click "add new". Choose the Contact Us form template or another free template and click "Create".

Next, edit or create a WordPress contact page. Click the "Formidable" button to open the shortcode generator. Choose your new web form and insert it into the WordPress page. Save the page for a beautiful WP contact form, ready to collect and store your leads. The contact form template will get you up and running fast.

= Why isn't WordPress sending emails? =
When you do not receive emails, try the following steps:

   1. Double check the email address in your Email action on the settings page. The [admin_email] shortcode uses the email address from your WordPress Settings -> General page.
   2. Are you receiving other emails from your site (ie comment notifications, forgot password...)? If not, notifications will not work either.
   3. Check your SPAM box.
   4. Try a different address in your settings.
   5. Install WP Mail SMTP or another similar emailing alternative and configure the SMTP settings.
   6. If these steps do not fix the problem and other WP signup emails are not going out, please reach out to your web host.

[Read more about WordPress emails not sending](https://formidableforms.com/wordpress-not-sending-emails-smtp/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion) in our blog.

= What types of WordPress forms can I build? =
Combined with our add-ons, Formidable is the most powerful form maker on the market. Here are some types of web forms you can create:

* Custom Contact Form
* Multi-Page Form with progress bar
* Dynamic Form (where fields change based on user’s answers)
* Request a Quote Form
* Job Application Form
* Feedback Surveys
* Make a Suggestion
* Testimonials
* Change Requests
* Online Booking
* Event Booking
* Online RSVP Form
* Custom WordPress User Registration Form (Great for membership sites)
* WordPress Login Form
* Custom WordPress User Profile
* WordPress Post Submission (Great for guest posts)
* WooCommerce Form for Products
* Credit Card Form
* Make a Donation Form
* T-Shirt Order Form
* Product Purchase Form
* Lead Capture
* Auto Form with Car Make and Model
* Video Release
* Partnership Agreements
* PTO Request
* Online Petitions
* Signature Form
* Maintenance Request
* Scholarship Application Form
* File Download Form
* Employment Verification
* Make a Referral
* Membership, Customer, Vendor, Conference, and Volunteer Registration
* Camp, Course, School Class, and other Event Registration
* Custom Survey
* Polls
* Quizzes
* Mortgage Calculator
* Car Payment Calculator
* BMI Calculator
* User Age Calculator
* Online Quote Calculator
* Compound Interest Calculator
* Amortization Calculator
* Calorie Intake Calculator
* Pregnancy Due Date Calculator
* Days Between Dates
* Recipe Reviews
* Personality Quiz
* Viral Quiz
* Cosmos Style Quiz
* Create Your Own Adventure Quiz

To see more, visit our [Form Template Gallery](https://formidableforms.com/form-templates/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion) which has over 175 pre-made templates.

= Can I see any example online calculators? =
Sure! Here are just a few examples:
* [Advanced Mortgage Calculator](https://formidableforms.com/form-templates/advanced-mortgage-calculator-form/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion)
* [Basic Mortgage Calculator](https://formidableforms.com/form-templates/simple-mortgage-calculator-form/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion)
* [Car Payment Calculator](https://formidableforms.com/form-templates/automobile-payment-calculator-form/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion)
* [Net Promoter Score (NPS) Survey](https://formidableforms.com/form-templates/nps-survey-form/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion)
* [BMI Calculator](https://formidableforms.com/form-templates/bmi-calculator-form/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion) and more Health and Fitness Calculators
* [User Age Calculator](https://formidableforms.com/form-templates/age-calculator-form/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion)
* Online Quote Calculator
* Finance Calculator
* Booking Cost Calculator
* Product Price Calculator

= How can I get access to all advanced features? =
To get access to more features, integrations, and support, [upgrade to Formidable Pro](https://formidableforms.com/?utm_source=wprepo&utm_medium=link&utm_campaign=liteversion). A Pro license gives you access to the full version of Formidable for more advanced options, Formidable Views, graphs and stats, priority support, and Formidable Add-ons!

= Can I create a payment form? =

Yes! We make it easy to accept payments using Stripe, PayPal, and Authorize.net.

Our Stripe integration helps you quickly accept credit card payments online. Our PayPal forms allow you to accept PayPal payments, subscriptions, and donations online.

= Which field types does Formidable offer? =

Our custom form and quiz builder comes with all the powerful fields that you need to create a solution-focused form, fast!

* Single line text - Great for name, phone number, address, and more.
* Email
* Website/URL
* Paragraph text
* Checkboxes and radio buttons
* Dropdown select boxes
* Number
* Phone number
* Hidden fields
* User ID
* HTML block - Great for custom HTML
* Captcha for Google reCAPTCHA (invisible V2 or checkbox V2, V3), hCaptcha, or Cloudflare Turnstile.
* GDPR - Great for compliance with General Data Protection Regulation (GDPR).

Here is a list of our advanced premium fields that will come in handy:

* File Upload
* Rich Text
* Date
* Time
* Scale
* Star Rating
* Ranking
* Range Slider
* Toggle
* Dynamic - Great for creating relationships between entries.
* Repeater - Great for registering multiple kids, job history, and much more.
* Tags
* Summary - Great to review responses before submit.
* Lookup - Great for cascading lookups like country, city, state.
* Section Heading
* Page Break
* Embed Form - Great for reusing the same set of fields in multiple places.
* Password Field
* Address Field - Power it up with Google address autofill and geolocation.
* Signature - Great for contracts and booking.

Additionally, our Payment fields will help you create a credit card form, donation form, or booking form.

* Single Item
* Multiple Items
* Checkbox Items
* Dropdown Items
* Product Quantity
* Total
* Credit Card (Stripe or Authorize.net)

= Can I import and export submissions? =

Yes, it's easy to import and export. This is incredibly useful for developers and agencies who are building websites for clients. You can also create custom form templates to use on client websites.

You can also import from other WordPress contact form plugins such as [Gravity Forms](https://wordpress.org/plugins/formidable-gravity-forms-importer/) and Pirate Forms. Although we don't have an importer available, this is also a great Caldera Forms alternative since it's no longer supported.

= Can I integrate with my CRM or email marketing service? =

Yes! We know that marketing is the key to growing your business. That's why Formidable allows you to connect your WP form (email form, payment form, etc.) with the marketing platform of your choice. You can easily send data from WordPress to your favorite CRM, email newsletter, and other marketing platforms.

Here is a list of our CRM and email marketing integrations:

* Mailchimp
* AWeber
* Constant Contact
* GetResponse
* MailPoet
* Active Campaign
* Salesforce CRM
* HubSpot CRM
* Campaign Monitor
* Highrise CRM

Using our Zapier integration, you can easily connect your website with over 5,000+ marketing apps including:

* SendInBlue
* Zoho CRM
* Zoho Mail
* Zoho Invoice
* Agile CRM
* Slack
* Trello
* Infusionsoft by Keap
* Microsoft Excel
* Dropbox
* PipeDrive CRM
* HelpScout
* Zendesk
* Freshbooks
* Freshsales
* Intercom
* Click Funnels
* Microsoft Dynamics 365 CRM
* Capsule CRM
* Insightly CRM
* Printfection
* Acuity Scheduling
* Quickbooks Online

See all [Formidable Zapier Integrations](https://zapier.com/apps/formidable/integrations).

== Changelog ==
= 6.21.1 =
* New: A new setting, Check denylist data to validate for spam, has been added to Global spam settings. This new spam check was causing too many false positives, so it is now disabled by default.
* New: When a denylist check is enabled, spam keywords that are detected are now stored in a transient and displayed in Global spam settings, under Custom allowed words. This makes it easier to detect and add exceptions when spam is detected.
* New: Denylist checks will no longer check radio buttons, checkboxes, dropdowns, signature, password, and CAPTCHA fields to help avoid issues with false positive matches. Fields with options will still validate "Other" input values.
* New: All spam checks are now disabled when importing forms.
* Fix: The way the honeypot field ID is determined has been updated to avoid conflicts with other forms.
* Fix: User defined product fields would fail validation.

= 6.21 =
* New: Honeypot field settings have been moved from form settings to global settings. The new settings can now be found in the Captcha/Spam section. The honeypot implementation has also been updated to make the honeypot more difficult to tell apart from other fields, and the strict option has been removed to help prevent issues with false positives for iOS users.
* New: A new setting to validate form entries for spam against the stopforumspam API have been added to form settings. When this is enabled, IPs and email addresses will be validated against a spam database.
* New: Field data is now compared to a comprehensive denylist during field validation. This significantly helps to block form data that looks like spam. New settings for Custom allowed words and Custom disallowed words have also been added.
* New: The option to compare submitted form entry data to WordPress spam comments has been added to global settings.
* New: Option values are now validated by default. If this causes issues with custom logic, this can be reverted using add_filter( 'frm_option_is_valid', '__return_true' );.
* New: Stripe payment fields will now apply border radius styles to match styling better.
* New: Stripe payment field label styling has been updated to better reflect label padding style settings.
* New: Stripe payment fields will now use the input weight style setting to look more consistent with other fields.
* New: Stripe payment fields now support the font family style setting.
* New: A new layout setting has been added to Stripe settings that adds support for the accordion layout.
* Fix: Paragraph fields would not properly display placeholders on the builder page.
* Fix: A fatal error that could happen on form submit when unexpected data was sent when checking for spam has been fixed.
* Fix: A Cannot set properties of null JavaScript error when duplicating a field has been fixed.
* Fix: Sorting entries by a number field value would sort as strings instead of numbers.
* Fix: Stripe payment fields would incorrectly use the wrong style settings in some cases.
* The small device message can now be dismissed, allowing people to still edit forms while using a phone.

= 6.20 =
* Security: Shortcodes in emails would process more than once.
* New: Over 30 free form templates are now available automatically for all users that would previously require a code sent through email.
* New: Admin pages have been modified to work better on smaller screen sizes.
* New: The GDPR field agreement text now supports links.
* Fix: Additional checks have been added to ensure that a GDPR field is always required.
* Fix: A Passing null to parameter deprecated message when viewing GDPR field settings has been fixed.
* Fix: A conflict with WPML would cause querying issues resulting in empty results when checking for form actions. This would cause the fallback confirmation action to appear even when there were valid confirmation actions in a form.
* Additional validation has been checked when outputting CSS variables when generating a stylesheet to help make sure the generated CSS is valid.
* Some additional validation has been added to help prevent issues where invalid serialized data would cause partial serialized strings to appear in setting values.
* The preview page for the contact form that gets installed automatically on every site is now automatically blocked from anyone without access to view the forms list. In addition, the preview page will no longer display a form when an incorrect key is used. This is to help prevent spam that targets the default form. A new frm_block_preview filter has been added which can be used to unblock the default form, and to block additional forms.

= 6.19 =
* New: A new Enable GDPR related features and enhancements setting has been added to Global Settings. When enabled, a new GDPR field is available in the form builder. The GDPR field is a special required checkbox field that must be checked in order for the form to be submitted.
* New: Sorting preferences are now remembered on the Forms and Entries list admin pages. Forms are now also automatically sorted with the newest forms at the top.
* New: A quick link to the views tab is now available on the form list admin page.
* New: A new setting to disable cookies has been added to Global Settings in the GDPR section.
* New: The enter key will now quickly jump between inputs when setting options for Radio Button, Checkbox, and Dropdown fields.
* Fix: The accordions on the visual styler page looked incorrect when using WordPress v6.7.2.
* Fix: After updating an entry, empty user ID values would convert to 0, causing issues when trying to filter a view with an empty user ID.
* Fix: Turnstile captcha fields would not properly reset on an error when submitting with AJAX.
* Fix: A Javascript error would occur when triggering a change event on a hidden field with custom code.

[See changelog for all versions](https://raw.githubusercontent.com/Strategy11/formidable-forms/master/changelog.txt)

== Upgrade Notice ==
= 6.20 =
This version fixes a security-related bug. Upgrade immediately.
