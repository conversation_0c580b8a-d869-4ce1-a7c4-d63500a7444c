<?php
if ( ! defined( 'ABSPATH' ) ) {
	die( 'You are not allowed to call this page directly.' );
}
?>
<div id="form_show_entry_page" class="frm_wrap frm_single_entry_page">
	<div>
		<?php
		FrmAppHelper::get_admin_header(
			array(
				'label'      => __( 'View Entry', 'formidable' ),
				'form'       => $form,
				'hide_title' => true,
				'close'      => '?page=formidable-entries&form=' . $form->id,
			)
		);
		?>

		<div class="columns-2">

		<div id="post-body-content" class="frm-fields">
			<?php
			/**
			 * Fires after the `#post-body-content` element on the entry show page.
			 *
			 * This action is used in Pro to display the pagination buttons. It allows developers
			 * to add custom HTML content after the main post body content.
			 *
			 * @since 6.4.1
			 *
			 * @param array $args Associative array with 'id' for entry ID and 'form' for form object.
			 *
			 * @type int    $args['id']   The ID of the entry.
			 * @type object $args['form'] The form object.
			 */
			do_action( 'frm_show_entry_start_content', compact( 'id', 'form' ) );
			?>

			<div class="wrap frm-with-margin frm_form_fields">
				<div class="postbox">
					<a href="#" id="frm-entry-show-empty-fields" class="alignright frm-pre-hndle" data-frmtoggle=".frm-empty-row" data-toggletext="<?php esc_attr_e( 'Hide empty fields', 'formidable' ); ?>">
						<?php esc_html_e( 'Show empty fields', 'formidable' ); ?>
					</a>
					<h3 class="hndle">
						<span><?php esc_html_e( 'Entry', 'formidable' ); ?></span>
						<span class="frm-sub-label">
							<?php
							printf(
								/* translators: %d: Entry ID */
								esc_html__( '(ID %d)', 'formidable' ),
								esc_attr( $entry->id )
							);
							?>
						</span>
					</h3>
					<?php
					$show_args = array(
						'id'             => $entry->id,
						'entry'          => $entry,
						'fields'         => $fields,
						'include_blank'  => true,
						'include_extras' => 'page, section, password',
						'inline_style'   => 0,
						'class'          => 'frm-alt-table',
						'show_filename'  => true,
						'show_image'     => true,
						'size'           => 'thumbnail',
						'add_link'       => true,
					);

					/**
					 * Allows modifying the arguments when showing entry in the Entries page.
					 *
					 * @since 5.0.16
					 *
					 * @param array $show_args The arguments.
					 * @param array $args      Includes `form`.
					 */
					$show_args = apply_filters( 'frm_entries_show_args', $show_args, compact( 'form' ) );

					echo FrmEntriesController::show_entry_shortcode( $show_args ); // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
					?>

					<?php do_action( 'frm_show_entry', $entry ); ?>
				</div>

				<?php do_action( 'frm_after_show_entry', $entry ); ?>
			</div>
		</div>

		<div class="frm-right-panel">
			<?php
			do_action( 'frm_show_entry_sidebar', $entry );
			FrmEntriesController::entry_sidebar( $entry );
			?>
		</div>

		</div>
	</div>
</div>
