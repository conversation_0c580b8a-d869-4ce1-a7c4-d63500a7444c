<svg width="112" height="88" viewBox="0 0 112 88" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.21521" y="0.5" width="87" height="87" rx="7.5" fill="#F9FAFB" stroke="#EAECF0"/>
<rect x="8.71521" y="8" width="72" height="16" rx="4" fill="#EAECF0"/>
<rect x="9.21521" y="30.5" width="71" height="31" rx="3.5" stroke="#C6CBD3" stroke-dasharray="3 3"/>
<g filter="url(#filter0_d_80_6757)">
<rect x="35.2849" y="41.1132" width="72" height="33" rx="4" fill="#C6CBD3"/>
<rect x="35.7849" y="41.6132" width="71" height="32" rx="3.5" stroke="#98A2B3"/>
</g>
<rect x="43.2849" y="49.1132" width="28" height="3" rx="1.5" fill="#F9FAFB"/>
<rect x="43.2849" y="56.1132" width="18" height="3" rx="1.5" fill="#F9FAFB"/>
<rect x="43.2849" y="63.1132" width="10" height="3" rx="1.5" fill="#F9FAFB"/>
<defs>
<filter id="filter0_d_80_6757" x="31.2849" y="39.1132" width="80" height="41" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.13 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_80_6757"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_80_6757" result="shape"/>
</filter>
</defs>
</svg>
