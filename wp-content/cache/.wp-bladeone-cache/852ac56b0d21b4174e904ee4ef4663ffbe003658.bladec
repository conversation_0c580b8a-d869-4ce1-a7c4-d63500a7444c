<?php 
	/**
	 * Header Template.
	 *
	 * @param array 	$block The block settings and attributes.
	 * @param string 	$data The block data.
	 **/
 use NeurodiversityMan\Helpers\GlobalFunction;
 ?>

<header id="<?php echo \htmlentities(esc_attr($block['id'])??'', ENT_QUOTES, 'UTF-8', false); ?>" class="<?php echo \htmlentities(esc_attr($block['className'])??'', ENT_QUOTES, 'UTF-8', false); ?> ">
	<div class="py-2 bg-<?php echo \htmlentities(GlobalFunction::get_theme_color_class(get_theme_mod('header_background_color'), 'bg')??'', ENT_QUOTES, 'UTF-8', false); ?>">
 <div class="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4">
 <a class="flex items-center space-x-3 rtl:space-x-reverse" href="<?php echo \htmlentities(home_url('/')??'', ENT_QUOTES, 'UTF-8', false); ?>">
 <?php if(get_custom_logo()): ?>
 <?php 
 $custom_logo_id = get_theme_mod('custom_logo');
 $logo_url = wp_get_attachment_image_url($custom_logo_id, 'full');
  ?>
 <img src="<?php echo \htmlentities($logo_url??'', ENT_QUOTES, 'UTF-8', false); ?>" class="h-8" alt="<?php echo \htmlentities(get_bloginfo('name', 'display')??'', ENT_QUOTES, 'UTF-8', false); ?>"/>
 <?php if(get_theme_mod('show_title_with_logo')): ?>
 <span
 class="self-center text-2xl font-semibold whitespace-nowrap"><?php echo \htmlentities(get_bloginfo('name', 'display')??'', ENT_QUOTES, 'UTF-8', false); ?></span>
 <?php endif; ?>
 <?php else: ?>
 <span
 class="self-center text-2xl font-semibold whitespace-nowrap dark:text-white"><?php echo get_bloginfo('name', 'display'); ?></span>
 <?php endif; ?>
 </a>
 <button data-collapse-toggle="navbar-dropdown" type="button"
 class="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200"
 aria-controls="navbar-dropdown" aria-expanded="false">
 <span class="sr-only">Open main menu</span>
 <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
  viewBox="0 0 17 14">
 <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
   d="M1 1h15M1 7h15M1 13h15"/>
 </svg>
 </button>
 <?php echo wp_nav_menu([
 'theme_location' => 'primary_navigation',
 'container' => 'div',
 'container_class' => 'hidden w-full md:block md:w-auto',
 'container_id' => 'navbar-dropdown',
 'menu_class' => 'flex flex-col font-medium p-4 md:p-0 mt-4 border border-gray-100 rounded-lg md:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0',
 'fallback_cb' => false,
//				'walker' => new \NeurodiversityMan\Helpers\NavWalker()
 ]); ?>

 </div>
	</div>
</header>
